---
title: wuzhen-gateway
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.23"

---

# wuzhen-gateway

Base URLs:

# Authentication

- HTTP Authentication, scheme: bearer

# 自定义存储

## POST 存储自定义值

POST /storage/{key}

> Body 请求参数

```json
{
  "storageData": "test_data"
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|key|path|string| 是 ||none|
|body|body|object| 否 ||none|
|» storageData|body|string| 是 | 值|none|

> 返回示例

```json
{
  "msg": "success",
  "code": 200
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» msg|string|true|none||none|
|» code|integer|true|none||none|

## GET 获取自定义值

GET /storage/{key}

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|key|path|string| 是 ||none|

> 返回示例

```json
{
  "msg": "success",
  "code": 200,
  "data": {
    "storageData": "test_data"
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» msg|string|true|none||none|
|» code|integer|true|none||none|
|» data|object|true|none||none|
|»» storageData|string|true|none||none|

## DELETE 删除自定义值

DELETE /api/storage/{key}

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|key|path|string| 是 ||none|

> 返回示例

```json
{
  "msg": "success",
  "code": 200
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» msg|string|true|none||none|
|» code|integer|true|none||none|

# 数据模型
