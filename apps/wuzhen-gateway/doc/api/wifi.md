---
title: wuzhen-gateway
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.23"

---

# wuzhen-gateway

Base URLs:

# Authentication

- HTTP Authentication, scheme: bearer

# wifi

## GET 获取上游WIFI连接信息

GET /upstream

> 返回示例

```json
{
  "msg": "success",
  "code": 200,
  "data": {
    "ssid": "AcmeGuest",
    "password": "123123123123"
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» msg|string|true|none||none|
|» code|integer|true|none||none|
|» data|object|true|none||none|
|»» ssid|string|true|none||none|
|»» password|string|true|none||none|

## POST 设置上游WIFI

POST /upstream

> Body 请求参数

```json
{
  "ssid": "AcmeGuest",
  "password": "123123123123"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|object| 否 |none|
|» ssid|body|string| 是 |none|
|» password|body|string| 是 |none|

> 返回示例

```json
{
  "msg": "success",
  "code": 200
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» msg|string|true|none||none|
|» code|integer|true|none||none|

## GET 获取下游WIFI连接信息

GET /downstream

> 返回示例

```json
{
  "msg": "success",
  "code": 200,
  "data": {
    "ssid": "x-net-6222",
    "password": "88888888222"
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» msg|string|true|none||none|
|» code|integer|true|none||none|
|» data|object|true|none||none|
|»» ssid|string|true|none||none|
|»» password|string|true|none||none|

## POST 设置下游WIFI

POST /downstream

> Body 请求参数

```json
{
  "ssid": "x-net-6222",
  "password": "88888888222"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|object| 否 |none|
|» ssid|body|string| 是 |none|
|» password|body|string| 是 |none|

> 返回示例

```json
{
  "msg": "success",
  "code": 200
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» msg|string|true|none||none|
|» code|integer|true|none||none|

## GET 获取WIFI列表

GET /wifiList

> 返回示例

```json
{
  "msg": "success",
  "code": 200,
  "data": [
    {
      "SSID": "AcmeGuest",
      "Signal": -42
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» msg|string|true|none||none|
|» code|integer|true|none||none|
|» data|[object]|true|none||none|
|»» SSID|string|true|none||none|
|»» Signal|integer|true|none||none|

# 数据模型
