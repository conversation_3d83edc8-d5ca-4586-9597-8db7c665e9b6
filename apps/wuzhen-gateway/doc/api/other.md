---
title: wuzhen-gateway
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.23"

---

# wuzhen-gateway

Base URLs:

# Authentication

- HTTP Authentication, scheme: bearer

# Default

## GET 大屏信息接口

GET /screen

> Body 请求参数

```json
{
  "password": "1d22d82c901358e1d3239adb3abc480fb784266f6f0d8ebee152c29a4df67b96"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|object| 否 |none|
|» password|body|string| 是 |none|

> 返回示例

```json
{
  "msg": "success",
  "code": 200,
  "data": {
    "proxy_info": {
      "name": "default(*************)-36fd",
      "proxies": [
        {
          "name": "新加坡-137",
          "server": "152.42.226.*",
          "port": 4560,
          "protocol": "ss",
          "country_name": "Singapore",
          "country_code": "sg",
          "city_name": "Singapore",
          "delay": 90,
          "use_status": "use",
          "ingress_ip": "152.42.226.*",
          "ip": "************",
          "ingress_country_name": "Singapore",
          "ingress_country_code": "sg",
          "ingress_city_name": "Singapore",
          "type": "guard",
          "source": "platform"
        },
        {
          "name": "加拿大-多伦多-191",
          "server": "38.132.122.*",
          "port": 4565,
          "protocol": "ss",
          "country_name": "Canada",
          "country_code": "ca",
          "city_name": "Toronto",
          "delay": 286,
          "use_status": "use",
          "ingress_ip": "38.132.122.*",
          "ip": "**************",
          "ingress_country_name": "USA",
          "ingress_country_code": "us",
          "ingress_city_name": "NewYork",
          "type": "exit",
          "source": "platform"
        }
      ],
      "exclusive": "",
      "wg": false,
      "change_time": 0,
      "change_at": 0
    },
    "version": "v1.4.4-25-gcb89c94",
    "total_devices": 7,
    "active_devices": 3,
    "idle_devices": 4,
    "proxy_region_map": {
      "bd": 1,
      "br": 1,
      "ca": 2,
      "ch": 2,
      "de": 2,
      "es": 2,
      "gb": 4,
      "hk": 2,
      "in": 1,
      "jp": 2,
      "lk": 1,
      "mx": 1,
      "sg": 2,
      "tr": 1,
      "us": 6
    },
    "path_list": [
      {
        "name": "default(*************)-36fd",
        "proxies": [
          "新加坡-137",
          "加拿大-多伦多-191"
        ],
        "use": true,
        "proxies_code": [
          "sg",
          "ca"
        ],
        "exclusive": "none",
        "account": "admin",
        "account_is_admin": true,
        "current_ip_use": false,
        "change_time": 0,
        "change_country_array": null
      },
      {
        "name": "default(***********)-be59",
        "proxies": [
          "香港-177",
          "孟加拉国-达卡-216"
        ],
        "use": true,
        "proxies_code": [
          "hk",
          "bd"
        ],
        "exclusive": "none",
        "account": "danbai",
        "account_is_admin": false,
        "current_ip_use": false,
        "change_time": 0,
        "change_country_array": null
      }
    ],
    "proxies_data": [
      {
        "Name": "孟加拉国-达卡-216",
        "Server": "**************",
        "Port": 4569,
        "Type": "exit",
        "Protocol": "ss",
        "CountryName": "Bangladesh",
        "CountryCode": "bd",
        "CityName": "Dhaka",
        "Delay": 94,
        "Raw": null,
        "UseStatus": "use",
        "IngressIP": "**************",
        "IP": "***********",
        "IngressCountryName": "Singapore",
        "IngressCountryCode": "sg",
        "IngressCityName": "Singapore",
        "Source": "platform",
        "CreatedAt": **********,
        "UpdatedAt": **********
      },
      {
        "Name": "加拿大-多伦多-191",
        "Server": "**************",
        "Port": 4565,
        "Type": "exit",
        "Protocol": "ss",
        "CountryName": "Canada",
        "CountryCode": "ca",
        "CityName": "Toronto",
        "Delay": 286,
        "Raw": null,
        "UseStatus": "use",
        "IngressIP": "**************",
        "IP": "**************",
        "IngressCountryName": "USA",
        "IngressCountryCode": "us",
        "IngressCityName": "NewYork",
        "Source": "platform",
        "CreatedAt": 1728700333,
        "UpdatedAt": 1728718213
      },
      {
        "Name": "英国-伦敦-147",
        "Server": "************",
        "Port": 4560,
        "Type": "exit",
        "Protocol": "ss",
        "CountryName": "UnitedKingdom",
        "CountryCode": "gb",
        "CityName": "London",
        "Delay": 346,
        "Raw": null,
        "UseStatus": "none",
        "IngressIP": "************",
        "IP": "*************",
        "IngressCountryName": "UnitedKingdom",
        "IngressCountryCode": "gb",
        "IngressCityName": "London",
        "Source": "platform",
        "CreatedAt": 1728717227,
        "UpdatedAt": 1728718223
      },
      {
        "Name": "英国-伦敦-148",
        "Server": "************",
        "Port": 4561,
        "Type": "exit",
        "Protocol": "ss",
        "CountryName": "UnitedKingdom",
        "CountryCode": "gb",
        "CityName": "London",
        "Delay": 333,
        "Raw": null,
        "UseStatus": "none",
        "IngressIP": "************",
        "IP": "*************",
        "IngressCountryName": "UnitedKingdom",
        "IngressCountryCode": "gb",
        "IngressCityName": "London",
        "Source": "platform",
        "CreatedAt": 1728717227,
        "UpdatedAt": 1728718224
      },
      {
        "Name": "英国-曼彻斯顿-149",
        "Server": "************",
        "Port": 4562,
        "Type": "exit",
        "Protocol": "ss",
        "CountryName": "UnitedKingdom",
        "CountryCode": "gb",
        "CityName": "Manchester",
        "Delay": -1,
        "Raw": null,
        "UseStatus": "none",
        "IngressIP": "************",
        "IP": "**************",
        "IngressCountryName": "UnitedKingdom",
        "IngressCountryCode": "gb",
        "IngressCityName": "London",
        "Source": "platform",
        "CreatedAt": 1728717227,
        "UpdatedAt": 1728718225
      },
      {
        "Name": "英国-曼彻斯顿-150",
        "Server": "************",
        "Port": 4563,
        "Type": "exit",
        "Protocol": "ss",
        "CountryName": "UnitedKingdom",
        "CountryCode": "gb",
        "CityName": "Manchester",
        "Delay": 344,
        "Raw": null,
        "UseStatus": "none",
        "IngressIP": "************",
        "IP": "**************",
        "IngressCountryName": "UnitedKingdom",
        "IngressCountryCode": "gb",
        "IngressCityName": "London",
        "Source": "platform",
        "CreatedAt": 1728717227,
        "UpdatedAt": 1728718227
      },
      {
        "Name": "瑞士-苏黎世-151",
        "Server": "************",
        "Port": 4564,
        "Type": "exit",
        "Protocol": "ss",
        "CountryName": "Switzerland",
        "CountryCode": "ch",
        "CityName": "Zurich",
        "Delay": 333,
        "Raw": null,
        "UseStatus": "none",
        "IngressIP": "************",
        "IP": "*************",
        "IngressCountryName": "UnitedKingdom",
        "IngressCountryCode": "gb",
        "IngressCityName": "London",
        "Source": "platform",
        "CreatedAt": 1728717227,
        "UpdatedAt": 1728718228
      },
      {
        "Name": "瑞士-苏黎世-152",
        "Server": "************",
        "Port": 4565,
        "Type": "exit",
        "Protocol": "ss",
        "CountryName": "Switzerland",
        "CountryCode": "ch",
        "CityName": "Zurich",
        "Delay": 337,
        "Raw": null,
        "UseStatus": "none",
        "IngressIP": "************",
        "IP": "*************",
        "IngressCountryName": "UnitedKingdom",
        "IngressCountryCode": "gb",
        "IngressCityName": "London",
        "Source": "platform",
        "CreatedAt": 1728717227,
        "UpdatedAt": 1728718229
      },
      {
        "Name": "德国-柏林-153",
        "Server": "************",
        "Port": 4566,
        "Type": "exit",
        "Protocol": "ss",
        "CountryName": "Germany",
        "CountryCode": "de",
        "CityName": "Berlin",
        "Delay": 319,
        "Raw": null,
        "UseStatus": "none",
        "IngressIP": "************",
        "IP": "**************",
        "IngressCountryName": "UnitedKingdom",
        "IngressCountryCode": "gb",
        "IngressCityName": "London",
        "Source": "platform",
        "CreatedAt": 1728717227,
        "UpdatedAt": 1728718230
      },
      {
        "Name": "德国-柏林-154",
        "Server": "************",
        "Port": 4567,
        "Type": "exit",
        "Protocol": "ss",
        "CountryName": "Germany",
        "CountryCode": "de",
        "CityName": "Berlin",
        "Delay": 335,
        "Raw": null,
        "UseStatus": "none",
        "IngressIP": "************",
        "IP": "**************",
        "IngressCountryName": "UnitedKingdom",
        "IngressCountryCode": "gb",
        "IngressCityName": "London",
        "Source": "platform",
        "CreatedAt": 1728717227,
        "UpdatedAt": 1728718231
      },
      {
        "Name": "西班牙-马德里-155",
        "Server": "************",
        "Port": 4568,
        "Type": "exit",
        "Protocol": "ss",
        "CountryName": "Spain",
        "CountryCode": "es",
        "CityName": "Madrid",
        "Delay": 336,
        "Raw": null,
        "UseStatus": "none",
        "IngressIP": "************",
        "IP": "************",
        "IngressCountryName": "UnitedKingdom",
        "IngressCountryCode": "gb",
        "IngressCityName": "London",
        "Source": "platform",
        "CreatedAt": 1728717227,
        "UpdatedAt": 1728718233
      },
      {
        "Name": "西班牙-马德里-156",
        "Server": "************",
        "Port": 4569,
        "Type": "exit",
        "Protocol": "ss",
        "CountryName": "Spain",
        "CountryCode": "es",
        "CityName": "Madrid",
        "Delay": 343,
        "Raw": null,
        "UseStatus": "none",
        "IngressIP": "************",
        "IP": "***************",
        "IngressCountryName": "UnitedKingdom",
        "IngressCountryCode": "gb",
        "IngressCityName": "London",
        "Source": "platform",
        "CreatedAt": 1728717227,
        "UpdatedAt": 1728718234
      },
      {
        "Name": "美国-芝加哥-186",
        "Server": "**************",
        "Port": 4560,
        "Type": "exit",
        "Protocol": "ss",
        "CountryName": "USA",
        "CountryCode": "us",
        "CityName": "Chicago",
        "Delay": 294,
        "Raw": null,
        "UseStatus": "none",
        "IngressIP": "**************",
        "IP": "***********",
        "IngressCountryName": "USA",
        "IngressCountryCode": "us",
        "IngressCityName": "NewYork",
        "Source": "platform",
        "CreatedAt": 1728717227,
        "UpdatedAt": 1728718235
      },
      {
        "Name": "美国-洛杉矶-187",
        "Server": "**************",
        "Port": 4561,
        "Type": "exit",
        "Protocol": "ss",
        "CountryName": "USA",
        "CountryCode": "us",
        "CityName": "LosAngeles",
        "Delay": 339,
        "Raw": null,
        "UseStatus": "none",
        "IngressIP": "**************",
        "IP": "*************",
        "IngressCountryName": "USA",
        "IngressCountryCode": "us",
        "IngressCityName": "NewYork",
        "Source": "platform",
        "CreatedAt": 1728717227,
        "UpdatedAt": 1728718236
      },
      {
        "Name": "美国-纽约-188",
        "Server": "**************",
        "Port": 4562,
        "Type": "exit",
        "Protocol": "ss",
        "CountryName": "USA",
        "CountryCode": "us",
        "CityName": "NewYork",
        "Delay": 271,
        "Raw": null,
        "UseStatus": "none",
        "IngressIP": "**************",
        "IP": "*************",
        "IngressCountryName": "USA",
        "IngressCountryCode": "us",
        "IngressCityName": "NewYork",
        "Source": "platform",
        "CreatedAt": 1728717227,
        "UpdatedAt": 1728718236
      },
      {
        "Name": "美国-纽约-189",
        "Server": "**************",
        "Port": 4563,
        "Type": "exit",
        "Protocol": "ss",
        "CountryName": "USA",
        "CountryCode": "us",
        "CityName": "NewYork",
        "Delay": 268,
        "Raw": null,
        "UseStatus": "none",
        "IngressIP": "**************",
        "IP": "**************",
        "IngressCountryName": "USA",
        "IngressCountryCode": "us",
        "IngressCityName": "NewYork",
        "Source": "platform",
        "CreatedAt": 1728717227,
        "UpdatedAt": 1728718238
      },
      {
        "Name": "加拿大-蒙特利尔-190",
        "Server": "**************",
        "Port": 4564,
        "Type": "exit",
        "Protocol": "ss",
        "CountryName": "Canada",
        "CountryCode": "ca",
        "CityName": "Montreal",
        "Delay": 267,
        "Raw": null,
        "UseStatus": "none",
        "IngressIP": "**************",
        "IP": "**************",
        "IngressCountryName": "USA",
        "IngressCountryCode": "us",
        "IngressCityName": "NewYork",
        "Source": "platform",
        "CreatedAt": 1728717227,
        "UpdatedAt": 1728718239
      },
      {
        "Name": "墨西哥-特雷塔罗-192",
        "Server": "**************",
        "Port": 4566,
        "Type": "exit",
        "Protocol": "ss",
        "CountryName": "Mexico",
        "CountryCode": "mx",
        "CityName": "Queretaro",
        "Delay": 377,
        "Raw": null,
        "UseStatus": "none",
        "IngressIP": "**************",
        "IP": "*************",
        "IngressCountryName": "USA",
        "IngressCountryCode": "us",
        "IngressCityName": "NewYork",
        "Source": "platform",
        "CreatedAt": 1728717227,
        "UpdatedAt": 1728718240
      },
      {
        "Name": "巴西-圣保罗-193",
        "Server": "**************",
        "Port": 4567,
        "Type": "exit",
        "Protocol": "ss",
        "CountryName": "Brazil",
        "CountryCode": "br",
        "CityName": "SaoPaulo",
        "Delay": 433,
        "Raw": null,
        "UseStatus": "none",
        "IngressIP": "**************",
        "IP": "**************",
        "IngressCountryName": "USA",
        "IngressCountryCode": "us",
        "IngressCityName": "NewYork",
        "Source": "platform",
        "CreatedAt": 1728717227,
        "UpdatedAt": 1728718241
      },
      {
        "Name": "美国-达拉斯-194",
        "Server": "**************",
        "Port": 4568,
        "Type": "exit",
        "Protocol": "ss",
        "CountryName": "USA",
        "CountryCode": "us",
        "CityName": "Dallas",
        "Delay": 301,
        "Raw": null,
        "UseStatus": "none",
        "IngressIP": "**************",
        "IP": "*************",
        "IngressCountryName": "USA",
        "IngressCountryCode": "us",
        "IngressCityName": "NewYork",
        "Source": "platform",
        "CreatedAt": 1728717227,
        "UpdatedAt": 1728718242
      },
      {
        "Name": "美国-西雅图-195",
        "Server": "**************",
        "Port": 4569,
        "Type": "exit",
        "Protocol": "ss",
        "CountryName": "USA",
        "CountryCode": "us",
        "CityName": "Seattle",
        "Delay": 407,
        "Raw": null,
        "UseStatus": "none",
        "IngressIP": "**************",
        "IP": "**************",
        "IngressCountryName": "USA",
        "IngressCountryCode": "us",
        "IngressCityName": "NewYork",
        "Source": "platform",
        "CreatedAt": 1728717227,
        "UpdatedAt": 1728718243
      },
      {
        "Name": "新加坡-207",
        "Server": "**************",
        "Port": 4560,
        "Type": "exit",
        "Protocol": "ss",
        "CountryName": "Singapore",
        "CountryCode": "sg",
        "CityName": "Singapore",
        "Delay": -1,
        "Raw": null,
        "UseStatus": "none",
        "IngressIP": "**************",
        "IP": "*************",
        "IngressCountryName": "Singapore",
        "IngressCountryCode": "sg",
        "IngressCityName": "Singapore",
        "Source": "platform",
        "CreatedAt": 1728717227,
        "UpdatedAt": 1728718204
      },
      {
        "Name": "新加坡-208",
        "Server": "**************",
        "Port": 4561,
        "Type": "exit",
        "Protocol": "ss",
        "CountryName": "Singapore",
        "CountryCode": "sg",
        "CityName": "Singapore",
        "Delay": 113,
        "Raw": null,
        "UseStatus": "none",
        "IngressIP": "**************",
        "IP": "*************",
        "IngressCountryName": "Singapore",
        "IngressCountryCode": "sg",
        "IngressCityName": "Singapore",
        "Source": "platform",
        "CreatedAt": 1728717227,
        "UpdatedAt": 1728718206
      },
      {
        "Name": "香港-209",
        "Server": "**************",
        "Port": 4562,
        "Type": "exit",
        "Protocol": "ss",
        "CountryName": "HongKong",
        "CountryCode": "hk",
        "CityName": "HongKong",
        "Delay": 212,
        "Raw": null,
        "UseStatus": "none",
        "IngressIP": "**************",
        "IP": "**************",
        "IngressCountryName": "Singapore",
        "IngressCountryCode": "sg",
        "IngressCityName": "Singapore",
        "Source": "platform",
        "CreatedAt": 1728717227,
        "UpdatedAt": 1728718206
      },
      {
        "Name": "香港-210",
        "Server": "**************",
        "Port": 4563,
        "Type": "exit",
        "Protocol": "ss",
        "CountryName": "HongKong",
        "CountryCode": "hk",
        "CityName": "HongKong",
        "Delay": 143,
        "Raw": null,
        "UseStatus": "none",
        "IngressIP": "**************",
        "IP": "**************",
        "IngressCountryName": "Singapore",
        "IngressCountryCode": "sg",
        "IngressCityName": "Singapore",
        "Source": "platform",
        "CreatedAt": 1728717227,
        "UpdatedAt": 1728718207
      },
      {
        "Name": "日本-东京-211",
        "Server": "**************",
        "Port": 4564,
        "Type": "exit",
        "Protocol": "ss",
        "CountryName": "Japan",
        "CountryCode": "jp",
        "CityName": "Tokyo",
        "Delay": 158,
        "Raw": null,
        "UseStatus": "none",
        "IngressIP": "**************",
        "IP": "*************",
        "IngressCountryName": "Singapore",
        "IngressCountryCode": "sg",
        "IngressCityName": "Singapore",
        "Source": "platform",
        "CreatedAt": 1728717227,
        "UpdatedAt": 1728718207
      },
      {
        "Name": "日本-大阪-212",
        "Server": "**************",
        "Port": 4565,
        "Type": "exit",
        "Protocol": "ss",
        "CountryName": "Japan",
        "CountryCode": "jp",
        "CityName": "Osaka",
        "Delay": 174,
        "Raw": null,
        "UseStatus": "none",
        "IngressIP": "**************",
        "IP": "**********",
        "IngressCountryName": "Singapore",
        "IngressCountryCode": "sg",
        "IngressCityName": "Singapore",
        "Source": "platform",
        "CreatedAt": 1728717227,
        "UpdatedAt": 1728718208
      },
      {
        "Name": "土耳其-伊兹密尔-213",
        "Server": "**************",
        "Port": 4566,
        "Type": "exit",
        "Protocol": "ss",
        "CountryName": "Turkey",
        "CountryCode": "tr",
        "CityName": "Izmir",
        "Delay": 381,
        "Raw": null,
        "UseStatus": "none",
        "IngressIP": "**************",
        "IP": "***********",
        "IngressCountryName": "Singapore",
        "IngressCountryCode": "sg",
        "IngressCityName": "Singapore",
        "Source": "platform",
        "CreatedAt": 1728717227,
        "UpdatedAt": 1728718209
      },
      {
        "Name": "印度-孟买-214",
        "Server": "**************",
        "Port": 4567,
        "Type": "exit",
        "Protocol": "ss",
        "CountryName": "India",
        "CountryCode": "in",
        "CityName": "Mumbai",
        "Delay": 226,
        "Raw": null,
        "UseStatus": "none",
        "IngressIP": "**************",
        "IP": "*************",
        "IngressCountryName": "Singapore",
        "IngressCountryCode": "sg",
        "IngressCityName": "Singapore",
        "Source": "platform",
        "CreatedAt": 1728717227,
        "UpdatedAt": 1728718210
      },
      {
        "Name": "斯里兰卡-科伦坡-215",
        "Server": "**************",
        "Port": 4568,
        "Type": "exit",
        "Protocol": "ss",
        "CountryName": "SriLanka",
        "CountryCode": "lk",
        "CityName": "Colombo",
        "Delay": 223,
        "Raw": null,
        "UseStatus": "none",
        "IngressIP": "**************",
        "IP": "***********",
        "IngressCountryName": "Singapore",
        "IngressCountryCode": "sg",
        "IngressCityName": "Singapore",
        "Source": "platform",
        "CreatedAt": 1728717227,
        "UpdatedAt": 1728718211
      }
    ],
    "total_proxy_num": 30,
    "active_proxy_num": 2,
    "idle_proxy_num": 28,
    "proxy_region_best_delay_map": {
      "bd": 94,
      "br": 433,
      "ca": 267,
      "ch": 333,
      "de": 319,
      "es": 336,
      "gb": 333,
      "hk": 143,
      "in": 226,
      "jp": 158,
      "lk": 223,
      "mx": 377,
      "sg": 113,
      "tr": 381,
      "us": 268
    },
    "high_quality_proxy_num": 26,
    "mid_quality_proxy_num": 2,
    "low_quality_proxy_num": 0,
    "time_out_proxy_num": 2
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» msg|string|true|none||none|
|» code|integer|true|none||none|
|» data|object|true|none||none|
|»» proxy_info|object|true|none|当前使用链路信息|none|
|»»» name|string|true|none||none|
|»»» proxies|[object]|true|none||none|
|»»»» name|string|true|none||none|
|»»»» server|string|true|none||none|
|»»»» port|integer|true|none||none|
|»»»» protocol|string|true|none||none|
|»»»» country_name|string|true|none||none|
|»»»» country_code|string|true|none||none|
|»»»» city_name|string|true|none||none|
|»»»» delay|integer|true|none||none|
|»»»» use_status|string|true|none||none|
|»»»» ingress_ip|string|true|none||none|
|»»»» ip|string|true|none||none|
|»»»» ingress_country_name|string|true|none||none|
|»»»» ingress_country_code|string|true|none||none|
|»»»» ingress_city_name|string|true|none||none|
|»»»» type|string|true|none||none|
|»»»» source|string|true|none||none|
|»»» exclusive|string|true|none||none|
|»»» wg|boolean|true|none||none|
|»»» change_time|integer|true|none||none|
|»»» change_at|integer|true|none||none|
|»» version|string|true|none|版本|none|
|»» total_devices|integer|true|none|设备总数|none|
|»» active_devices|integer|true|none|活跃设备数|none|
|»» idle_devices|integer|true|none|空闲设备数|none|
|»» proxy_region_map|object|true|none|代理节点区域分布|none|
|»»» bd|integer|true|none||none|
|»»» br|integer|true|none||none|
|»»» ca|integer|true|none||none|
|»»» ch|integer|true|none||none|
|»»» de|integer|true|none||none|
|»»» es|integer|true|none||none|
|»»» gb|integer|true|none||none|
|»»» hk|integer|true|none||none|
|»»» in|integer|true|none||none|
|»»» jp|integer|true|none||none|
|»»» lk|integer|true|none||none|
|»»» mx|integer|true|none||none|
|»»» sg|integer|true|none||none|
|»»» tr|integer|true|none||none|
|»»» us|integer|true|none||none|
|»» path_list|[object]|true|none|链路列表|none|
|»»» name|string|true|none||none|
|»»» proxies|[string]|true|none||none|
|»»» use|boolean|true|none||none|
|»»» proxies_code|[string]|true|none||none|
|»»» exclusive|string|true|none||none|
|»»» account|string|true|none||none|
|»»» account_is_admin|boolean|true|none||none|
|»»» current_ip_use|boolean|true|none||none|
|»»» change_time|integer|true|none||none|
|»»» change_country_array|null|true|none||none|
|»» proxies_data|[object]|true|none|代理节点列表|none|
|»»» Name|string|true|none||none|
|»»» Server|string|true|none||none|
|»»» Port|integer|true|none||none|
|»»» Type|string|true|none||none|
|»»» Protocol|string|true|none||none|
|»»» CountryName|string|true|none||none|
|»»» CountryCode|string|true|none||none|
|»»» CityName|string|true|none||none|
|»»» Delay|integer|true|none||none|
|»»» Raw|null|true|none||none|
|»»» UseStatus|string|true|none||none|
|»»» IngressIP|string|true|none||none|
|»»» IP|string|true|none||none|
|»»» IngressCountryName|string|true|none||none|
|»»» IngressCountryCode|string|true|none||none|
|»»» IngressCityName|string|true|none||none|
|»»» Source|string|true|none||none|
|»»» CreatedAt|integer|true|none||none|
|»»» UpdatedAt|integer|true|none||none|
|»» total_proxy_num|integer|true|none|代理总数|none|
|»» active_proxy_num|integer|true|none|活跃代理数|none|
|»» idle_proxy_num|integer|true|none|空闲代理数|none|
|»» proxy_region_best_delay_map|object|true|none|代理区域最快延迟|none|
|»»» bd|integer|true|none||none|
|»»» br|integer|true|none||none|
|»»» ca|integer|true|none||none|
|»»» ch|integer|true|none||none|
|»»» de|integer|true|none||none|
|»»» es|integer|true|none||none|
|»»» gb|integer|true|none||none|
|»»» hk|integer|true|none||none|
|»»» in|integer|true|none||none|
|»»» jp|integer|true|none||none|
|»»» lk|integer|true|none||none|
|»»» mx|integer|true|none||none|
|»»» sg|integer|true|none||none|
|»»» tr|integer|true|none||none|
|»»» us|integer|true|none||none|
|»» high_quality_proxy_num|integer|true|none|高质量代理数|none|
|»» mid_quality_proxy_num|integer|true|none|中质量代理数|none|
|»» low_quality_proxy_num|integer|true|none|低质量代理数|none|
|»» time_out_proxy_num|integer|true|none|超时代理数|none|

# 数据模型
