---
title: wuzhen-gateway
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.23"

---

# wuzhen-gateway

Base URLs:

# Authentication

- HTTP Authentication, scheme: bearer

# 代理节点

## GET 代理节点列表

GET /proxies

> 返回示例

```json
{
  "msg": "success",
  "code": 200,
  "data": [
    {
      "name": "加拿大-多伦多-191",
      "server": "38.132.122.*",
      "port": 4565,
      "protocol": "ss",
      "country_name": "Canada",
      "country_code": "ca",
      "city_name": "Toronto",
      "delay": 337,
      "use_status": "none",
      "ingress_ip": "38.132.122.*",
      "ip": "**************",
      "ingress_country_name": "USA",
      "ingress_country_code": "us",
      "ingress_city_name": "NewYork",
      "type": "exit",
      "source": "platform"
    },
    {
      "name": "加拿大-蒙特利尔-190",
      "server": "38.132.122.*",
      "port": 4564,
      "protocol": "ss",
      "country_name": "Canada",
      "country_code": "ca",
      "city_name": "Montreal",
      "delay": 321,
      "use_status": "none",
      "ingress_ip": "38.132.122.*",
      "ip": "**************",
      "ingress_country_name": "USA",
      "ingress_country_code": "us",
      "ingress_city_name": "NewYork",
      "type": "exit",
      "source": "platform"
    },
    {
      "name": "印度-孟买-214",
      "server": "91.245.253.*",
      "port": 4567,
      "protocol": "ss",
      "country_name": "India",
      "country_code": "in",
      "city_name": "Mumbai",
      "delay": 84,
      "use_status": "none",
      "ingress_ip": "91.245.253.*",
      "ip": "*************",
      "ingress_country_name": "Singapore",
      "ingress_country_code": "sg",
      "ingress_city_name": "Singapore",
      "type": "exit",
      "source": "platform"
    },
    {
      "name": "印度尼西亚-雅加达-145",
      "server": "152.42.226.*",
      "port": 4568,
      "protocol": "ss",
      "country_name": "Indonesia",
      "country_code": "id",
      "city_name": "Jakarta",
      "delay": 103,
      "use_status": "none",
      "ingress_ip": "152.42.226.*",
      "ip": "**************",
      "ingress_country_name": "Singapore",
      "ingress_country_code": "sg",
      "ingress_city_name": "Singapore",
      "type": "guard",
      "source": "platform"
    },
    {
      "name": "印度尼西亚-雅加达-146",
      "server": "152.42.226.*",
      "port": 4569,
      "protocol": "ss",
      "country_name": "Indonesia",
      "country_code": "id",
      "city_name": "Jakarta",
      "delay": 104,
      "use_status": "none",
      "ingress_ip": "152.42.226.*",
      "ip": "**************",
      "ingress_country_name": "Singapore",
      "ingress_country_code": "sg",
      "ingress_city_name": "Singapore",
      "type": "guard",
      "source": "platform"
    },
    {
      "name": "印度尼西亚-雅加达-181",
      "server": "146.70.113.*",
      "port": 4565,
      "protocol": "ss",
      "country_name": "Indonesia",
      "country_code": "id",
      "city_name": "Jakarta",
      "delay": 128,
      "use_status": "none",
      "ingress_ip": "146.70.113.*",
      "ip": "**************",
      "ingress_country_name": "HongKong",
      "ingress_country_code": "hk",
      "ingress_city_name": "HongKong",
      "type": "guard",
      "source": "platform"
    },
    {
      "name": "土耳其-伊兹密尔-213",
      "server": "91.245.253.*",
      "port": 4566,
      "protocol": "ss",
      "country_name": "Turkey",
      "country_code": "tr",
      "city_name": "Izmir",
      "delay": 296,
      "use_status": "none",
      "ingress_ip": "91.245.253.*",
      "ip": "***********",
      "ingress_country_name": "Singapore",
      "ingress_country_code": "sg",
      "ingress_city_name": "Singapore",
      "type": "exit",
      "source": "platform"
    },
    {
      "name": "墨西哥-特雷塔罗-192",
      "server": "38.132.122.*",
      "port": 4566,
      "protocol": "ss",
      "country_name": "Mexico",
      "country_code": "mx",
      "city_name": "Queretaro",
      "delay": 413,
      "use_status": "none",
      "ingress_ip": "38.132.122.*",
      "ip": "*************",
      "ingress_country_name": "USA",
      "ingress_country_code": "us",
      "ingress_city_name": "NewYork",
      "type": "exit",
      "source": "platform"
    },
    {
      "name": "孟加拉国-达卡-216",
      "server": "91.245.253.*",
      "port": 4569,
      "protocol": "ss",
      "country_name": "Bangladesh",
      "country_code": "bd",
      "city_name": "Dhaka",
      "delay": 94,
      "use_status": "use",
      "ingress_ip": "91.245.253.*",
      "ip": "***********",
      "ingress_country_name": "Singapore",
      "ingress_country_code": "sg",
      "ingress_city_name": "Singapore",
      "type": "exit",
      "source": "platform"
    },
    {
      "name": "巴西-圣保罗-193",
      "server": "38.132.122.*",
      "port": 4567,
      "protocol": "ss",
      "country_name": "Brazil",
      "country_code": "br",
      "city_name": "SaoPaulo",
      "delay": 438,
      "use_status": "none",
      "ingress_ip": "38.132.122.*",
      "ip": "**************",
      "ingress_country_name": "USA",
      "ingress_country_code": "us",
      "ingress_city_name": "NewYork",
      "type": "exit",
      "source": "platform"
    },
    {
      "name": "德国-柏林-153",
      "server": "206.189.29.*",
      "port": 4566,
      "protocol": "ss",
      "country_name": "Germany",
      "country_code": "de",
      "city_name": "Berlin",
      "delay": 270,
      "use_status": "none",
      "ingress_ip": "206.189.29.*",
      "ip": "**************",
      "ingress_country_name": "UnitedKingdom",
      "ingress_country_code": "gb",
      "ingress_city_name": "London",
      "type": "exit",
      "source": "platform"
    },
    {
      "name": "德国-柏林-154",
      "server": "206.189.29.*",
      "port": 4567,
      "protocol": "ss",
      "country_name": "Germany",
      "country_code": "de",
      "city_name": "Berlin",
      "delay": 271,
      "use_status": "none",
      "ingress_ip": "206.189.29.*",
      "ip": "**************",
      "ingress_country_name": "UnitedKingdom",
      "ingress_country_code": "gb",
      "ingress_city_name": "London",
      "type": "exit",
      "source": "platform"
    },
    {
      "name": "斯里兰卡-科伦坡-215",
      "server": "91.245.253.*",
      "port": 4568,
      "protocol": "ss",
      "country_name": "SriLanka",
      "country_code": "lk",
      "city_name": "Colombo",
      "delay": 90,
      "use_status": "none",
      "ingress_ip": "91.245.253.*",
      "ip": "***********",
      "ingress_country_name": "Singapore",
      "ingress_country_code": "sg",
      "ingress_city_name": "Singapore",
      "type": "exit",
      "source": "platform"
    },
    {
      "name": "新加坡-137",
      "server": "152.42.226.*",
      "port": 4560,
      "protocol": "ss",
      "country_name": "Singapore",
      "country_code": "sg",
      "city_name": "Singapore",
      "delay": 84,
      "use_status": "use",
      "ingress_ip": "152.42.226.*",
      "ip": "************",
      "ingress_country_name": "Singapore",
      "ingress_country_code": "sg",
      "ingress_city_name": "Singapore",
      "type": "guard",
      "source": "platform"
    },
    {
      "name": "新加坡-138",
      "server": "152.42.226.*",
      "port": 4561,
      "protocol": "ss",
      "country_name": "Singapore",
      "country_code": "sg",
      "city_name": "Singapore",
      "delay": 79,
      "use_status": "none",
      "ingress_ip": "152.42.226.*",
      "ip": "**************",
      "ingress_country_name": "Singapore",
      "ingress_country_code": "sg",
      "ingress_city_name": "Singapore",
      "type": "guard",
      "source": "platform"
    },
    {
      "name": "新加坡-176",
      "server": "146.70.113.*",
      "port": 4560,
      "protocol": "ss",
      "country_name": "Singapore",
      "country_code": "sg",
      "city_name": "Singapore",
      "delay": 101,
      "use_status": "none",
      "ingress_ip": "146.70.113.*",
      "ip": "*************",
      "ingress_country_name": "HongKong",
      "ingress_country_code": "hk",
      "ingress_city_name": "HongKong",
      "type": "guard",
      "source": "platform"
    },
    {
      "name": "新加坡-207",
      "server": "91.245.253.*",
      "port": 4560,
      "protocol": "ss",
      "country_name": "Singapore",
      "country_code": "sg",
      "city_name": "Singapore",
      "delay": -1,
      "use_status": "none",
      "ingress_ip": "91.245.253.*",
      "ip": "*************",
      "ingress_country_name": "Singapore",
      "ingress_country_code": "sg",
      "ingress_city_name": "Singapore",
      "type": "exit",
      "source": "platform"
    },
    {
      "name": "新加坡-208",
      "server": "91.245.253.*",
      "port": 4561,
      "protocol": "ss",
      "country_name": "Singapore",
      "country_code": "sg",
      "city_name": "Singapore",
      "delay": 196,
      "use_status": "none",
      "ingress_ip": "91.245.253.*",
      "ip": "*************",
      "ingress_country_name": "Singapore",
      "ingress_country_code": "sg",
      "ingress_city_name": "Singapore",
      "type": "exit",
      "source": "platform"
    },
    {
      "name": "新西兰-奥克兰-183",
      "server": "146.70.113.*",
      "port": 4567,
      "protocol": "ss",
      "country_name": "NewZealand",
      "country_code": "nz",
      "city_name": "Auckland",
      "delay": 230,
      "use_status": "none",
      "ingress_ip": "146.70.113.*",
      "ip": "************",
      "ingress_country_name": "HongKong",
      "ingress_country_code": "hk",
      "ingress_city_name": "HongKong",
      "type": "guard",
      "source": "platform"
    },
    {
      "name": "日本-东京-141",
      "server": "152.42.226.*",
      "port": 4564,
      "protocol": "ss",
      "country_name": "Japan",
      "country_code": "jp",
      "city_name": "Tokyo",
      "delay": 156,
      "use_status": "none",
      "ingress_ip": "152.42.226.*",
      "ip": "**************",
      "ingress_country_name": "Singapore",
      "ingress_country_code": "sg",
      "ingress_city_name": "Singapore",
      "type": "guard",
      "source": "platform"
    },
    {
      "name": "日本-东京-142",
      "server": "152.42.226.*",
      "port": 4565,
      "protocol": "ss",
      "country_name": "Japan",
      "country_code": "jp",
      "city_name": "Tokyo",
      "delay": 147,
      "use_status": "none",
      "ingress_ip": "152.42.226.*",
      "ip": "**************",
      "ingress_country_name": "Singapore",
      "ingress_country_code": "sg",
      "ingress_city_name": "Singapore",
      "type": "guard",
      "source": "platform"
    },
    {
      "name": "日本-东京-179",
      "server": "146.70.113.*",
      "port": 4563,
      "protocol": "ss",
      "country_name": "Japan",
      "country_code": "jp",
      "city_name": "Tokyo",
      "delay": 110,
      "use_status": "none",
      "ingress_ip": "146.70.113.*",
      "ip": "**************",
      "ingress_country_name": "HongKong",
      "ingress_country_code": "hk",
      "ingress_city_name": "HongKong",
      "type": "guard",
      "source": "platform"
    },
    {
      "name": "日本-东京-211",
      "server": "91.245.253.*",
      "port": 4564,
      "protocol": "ss",
      "country_name": "Japan",
      "country_code": "jp",
      "city_name": "Tokyo",
      "delay": 150,
      "use_status": "use",
      "ingress_ip": "91.245.253.*",
      "ip": "*************",
      "ingress_country_name": "Singapore",
      "ingress_country_code": "sg",
      "ingress_city_name": "Singapore",
      "type": "exit",
      "source": "platform"
    },
    {
      "name": "日本-大阪-139",
      "server": "152.42.226.*",
      "port": 4562,
      "protocol": "ss",
      "country_name": "Japan",
      "country_code": "jp",
      "city_name": "Osaka",
      "delay": 171,
      "use_status": "none",
      "ingress_ip": "152.42.226.*",
      "ip": "*************",
      "ingress_country_name": "Singapore",
      "ingress_country_code": "sg",
      "ingress_city_name": "Singapore",
      "type": "guard",
      "source": "platform"
    },
    {
      "name": "日本-大阪-140",
      "server": "152.42.226.*",
      "port": 4563,
      "protocol": "ss",
      "country_name": "Japan",
      "country_code": "jp",
      "city_name": "Osaka",
      "delay": 156,
      "use_status": "none",
      "ingress_ip": "152.42.226.*",
      "ip": "**************",
      "ingress_country_name": "Singapore",
      "ingress_country_code": "sg",
      "ingress_city_name": "Singapore",
      "type": "guard",
      "source": "platform"
    },
    {
      "name": "日本-大阪-178",
      "server": "146.70.113.*",
      "port": 4562,
      "protocol": "ss",
      "country_name": "Japan",
      "country_code": "jp",
      "city_name": "Osaka",
      "delay": 101,
      "use_status": "none",
      "ingress_ip": "146.70.113.*",
      "ip": "**************",
      "ingress_country_name": "HongKong",
      "ingress_country_code": "hk",
      "ingress_city_name": "HongKong",
      "type": "guard",
      "source": "platform"
    },
    {
      "name": "日本-大阪-212",
      "server": "91.245.253.*",
      "port": 4565,
      "protocol": "ss",
      "country_name": "Japan",
      "country_code": "jp",
      "city_name": "Osaka",
      "delay": 167,
      "use_status": "none",
      "ingress_ip": "91.245.253.*",
      "ip": "**********",
      "ingress_country_name": "Singapore",
      "ingress_country_code": "sg",
      "ingress_city_name": "Singapore",
      "type": "exit",
      "source": "platform"
    },
    {
      "name": "泰国-曼谷-180",
      "server": "146.70.113.*",
      "port": 4564,
      "protocol": "ss",
      "country_name": "Thailand",
      "country_code": "th",
      "city_name": "Bangkok",
      "delay": 113,
      "use_status": "none",
      "ingress_ip": "146.70.113.*",
      "ip": "*************",
      "ingress_country_name": "HongKong",
      "ingress_country_code": "hk",
      "ingress_city_name": "HongKong",
      "type": "guard",
      "source": "platform"
    },
    {
      "name": "澳大利亚-悉尼-182",
      "server": "146.70.113.*",
      "port": 4566,
      "protocol": "ss",
      "country_name": "Australia",
      "country_code": "au",
      "city_name": "Sydney",
      "delay": 289,
      "use_status": "none",
      "ingress_ip": "146.70.113.*",
      "ip": "**************",
      "ingress_country_name": "HongKong",
      "ingress_country_code": "hk",
      "ingress_city_name": "HongKong",
      "type": "guard",
      "source": "platform"
    },
    {
      "name": "瑞士-苏黎世-151",
      "server": "206.189.29.*",
      "port": 4564,
      "protocol": "ss",
      "country_name": "Switzerland",
      "country_code": "ch",
      "city_name": "Zurich",
      "delay": 278,
      "use_status": "none",
      "ingress_ip": "206.189.29.*",
      "ip": "*************",
      "ingress_country_name": "UnitedKingdom",
      "ingress_country_code": "gb",
      "ingress_city_name": "London",
      "type": "exit",
      "source": "platform"
    },
    {
      "name": "瑞士-苏黎世-152",
      "server": "206.189.29.*",
      "port": 4565,
      "protocol": "ss",
      "country_name": "Switzerland",
      "country_code": "ch",
      "city_name": "Zurich",
      "delay": 277,
      "use_status": "none",
      "ingress_ip": "206.189.29.*",
      "ip": "*************",
      "ingress_country_name": "UnitedKingdom",
      "ingress_country_code": "gb",
      "ingress_city_name": "London",
      "type": "exit",
      "source": "platform"
    },
    {
      "name": "美国-洛杉矶-184",
      "server": "146.70.113.*",
      "port": 4568,
      "protocol": "ss",
      "country_name": "USA",
      "country_code": "us",
      "city_name": "LosAngeles",
      "delay": 203,
      "use_status": "none",
      "ingress_ip": "146.70.113.*",
      "ip": "*************",
      "ingress_country_name": "HongKong",
      "ingress_country_code": "hk",
      "ingress_city_name": "HongKong",
      "type": "guard",
      "source": "platform"
    },
    {
      "name": "美国-洛杉矶-187",
      "server": "38.132.122.*",
      "port": 4561,
      "protocol": "ss",
      "country_name": "USA",
      "country_code": "us",
      "city_name": "LosAngeles",
      "delay": 389,
      "use_status": "none",
      "ingress_ip": "38.132.122.*",
      "ip": "*************",
      "ingress_country_name": "USA",
      "ingress_country_code": "us",
      "ingress_city_name": "NewYork",
      "type": "exit",
      "source": "platform"
    },
    {
      "name": "美国-纽约-188",
      "server": "38.132.122.*",
      "port": 4562,
      "protocol": "ss",
      "country_name": "USA",
      "country_code": "us",
      "city_name": "NewYork",
      "delay": 346,
      "use_status": "none",
      "ingress_ip": "38.132.122.*",
      "ip": "*************",
      "ingress_country_name": "USA",
      "ingress_country_code": "us",
      "ingress_city_name": "NewYork",
      "type": "exit",
      "source": "platform"
    },
    {
      "name": "美国-纽约-189",
      "server": "38.132.122.*",
      "port": 4563,
      "protocol": "ss",
      "country_name": "USA",
      "country_code": "us",
      "city_name": "NewYork",
      "delay": 337,
      "use_status": "none",
      "ingress_ip": "38.132.122.*",
      "ip": "**************",
      "ingress_country_name": "USA",
      "ingress_country_code": "us",
      "ingress_city_name": "NewYork",
      "type": "exit",
      "source": "platform"
    },
    {
      "name": "美国-芝加哥-186",
      "server": "38.132.122.*",
      "port": 4560,
      "protocol": "ss",
      "country_name": "USA",
      "country_code": "us",
      "city_name": "Chicago",
      "delay": 342,
      "use_status": "none",
      "ingress_ip": "38.132.122.*",
      "ip": "***********",
      "ingress_country_name": "USA",
      "ingress_country_code": "us",
      "ingress_city_name": "NewYork",
      "type": "exit",
      "source": "platform"
    },
    {
      "name": "美国-西雅图-195",
      "server": "38.132.122.*",
      "port": 4569,
      "protocol": "ss",
      "country_name": "USA",
      "country_code": "us",
      "city_name": "Seattle",
      "delay": 405,
      "use_status": "none",
      "ingress_ip": "38.132.122.*",
      "ip": "**************",
      "ingress_country_name": "USA",
      "ingress_country_code": "us",
      "ingress_city_name": "NewYork",
      "type": "exit",
      "source": "platform"
    },
    {
      "name": "美国-达拉斯-185",
      "server": "146.70.113.*",
      "port": 4569,
      "protocol": "ss",
      "country_name": "USA",
      "country_code": "us",
      "city_name": "Dallas",
      "delay": 254,
      "use_status": "none",
      "ingress_ip": "146.70.113.*",
      "ip": "*************",
      "ingress_country_name": "HongKong",
      "ingress_country_code": "hk",
      "ingress_city_name": "HongKong",
      "type": "guard",
      "source": "platform"
    },
    {
      "name": "美国-达拉斯-194",
      "server": "38.132.122.*",
      "port": 4568,
      "protocol": "ss",
      "country_name": "USA",
      "country_code": "us",
      "city_name": "Dallas",
      "delay": 354,
      "use_status": "none",
      "ingress_ip": "38.132.122.*",
      "ip": "*************",
      "ingress_country_name": "USA",
      "ingress_country_code": "us",
      "ingress_city_name": "NewYork",
      "type": "exit",
      "source": "platform"
    },
    {
      "name": "英国-伦敦-147",
      "server": "206.189.29.*",
      "port": 4560,
      "protocol": "ss",
      "country_name": "UnitedKingdom",
      "country_code": "gb",
      "city_name": "London",
      "delay": 270,
      "use_status": "none",
      "ingress_ip": "206.189.29.*",
      "ip": "*************",
      "ingress_country_name": "UnitedKingdom",
      "ingress_country_code": "gb",
      "ingress_city_name": "London",
      "type": "exit",
      "source": "platform"
    },
    {
      "name": "英国-伦敦-148",
      "server": "206.189.29.*",
      "port": 4561,
      "protocol": "ss",
      "country_name": "UnitedKingdom",
      "country_code": "gb",
      "city_name": "London",
      "delay": 270,
      "use_status": "none",
      "ingress_ip": "206.189.29.*",
      "ip": "*************",
      "ingress_country_name": "UnitedKingdom",
      "ingress_country_code": "gb",
      "ingress_city_name": "London",
      "type": "exit",
      "source": "platform"
    },
    {
      "name": "英国-曼彻斯顿-149",
      "server": "206.189.29.*",
      "port": 4562,
      "protocol": "ss",
      "country_name": "UnitedKingdom",
      "country_code": "gb",
      "city_name": "Manchester",
      "delay": -1,
      "use_status": "none",
      "ingress_ip": "206.189.29.*",
      "ip": "**************",
      "ingress_country_name": "UnitedKingdom",
      "ingress_country_code": "gb",
      "ingress_city_name": "London",
      "type": "exit",
      "source": "platform"
    },
    {
      "name": "英国-曼彻斯顿-150",
      "server": "206.189.29.*",
      "port": 4563,
      "protocol": "ss",
      "country_name": "UnitedKingdom",
      "country_code": "gb",
      "city_name": "Manchester",
      "delay": 304,
      "use_status": "none",
      "ingress_ip": "206.189.29.*",
      "ip": "**************",
      "ingress_country_name": "UnitedKingdom",
      "ingress_country_code": "gb",
      "ingress_city_name": "London",
      "type": "exit",
      "source": "platform"
    },
    {
      "name": "西班牙-马德里-155",
      "server": "206.189.29.*",
      "port": 4568,
      "protocol": "ss",
      "country_name": "Spain",
      "country_code": "es",
      "city_name": "Madrid",
      "delay": 286,
      "use_status": "none",
      "ingress_ip": "206.189.29.*",
      "ip": "************",
      "ingress_country_name": "UnitedKingdom",
      "ingress_country_code": "gb",
      "ingress_city_name": "London",
      "type": "exit",
      "source": "platform"
    },
    {
      "name": "西班牙-马德里-156",
      "server": "206.189.29.*",
      "port": 4569,
      "protocol": "ss",
      "country_name": "Spain",
      "country_code": "es",
      "city_name": "Madrid",
      "delay": 291,
      "use_status": "none",
      "ingress_ip": "206.189.29.*",
      "ip": "***************",
      "ingress_country_name": "UnitedKingdom",
      "ingress_country_code": "gb",
      "ingress_city_name": "London",
      "type": "exit",
      "source": "platform"
    },
    {
      "name": "香港-143",
      "server": "152.42.226.*",
      "port": 4566,
      "protocol": "ss",
      "country_name": "Hong Kong",
      "country_code": "hk",
      "city_name": "Hong Kong",
      "delay": 134,
      "use_status": "none",
      "ingress_ip": "152.42.226.*",
      "ip": "**************",
      "ingress_country_name": "Singapore",
      "ingress_country_code": "sg",
      "ingress_city_name": "Singapore",
      "type": "guard",
      "source": "platform"
    },
    {
      "name": "香港-144",
      "server": "152.42.226.*",
      "port": 4567,
      "protocol": "ss",
      "country_name": "Hong Kong",
      "country_code": "hk",
      "city_name": "Hong Kong",
      "delay": 122,
      "use_status": "none",
      "ingress_ip": "152.42.226.*",
      "ip": "************",
      "ingress_country_name": "Singapore",
      "ingress_country_code": "sg",
      "ingress_city_name": "Singapore",
      "type": "guard",
      "source": "platform"
    },
    {
      "name": "香港-177",
      "server": "146.70.113.*",
      "port": 4561,
      "protocol": "ss",
      "country_name": "HongKong",
      "country_code": "hk",
      "city_name": "HongKong",
      "delay": 55,
      "use_status": "use",
      "ingress_ip": "146.70.113.*",
      "ip": "*************",
      "ingress_country_name": "HongKong",
      "ingress_country_code": "hk",
      "ingress_city_name": "HongKong",
      "type": "guard",
      "source": "platform"
    },
    {
      "name": "香港-209",
      "server": "91.245.253.*",
      "port": 4562,
      "protocol": "ss",
      "country_name": "HongKong",
      "country_code": "hk",
      "city_name": "HongKong",
      "delay": 191,
      "use_status": "none",
      "ingress_ip": "91.245.253.*",
      "ip": "**************",
      "ingress_country_name": "Singapore",
      "ingress_country_code": "sg",
      "ingress_city_name": "Singapore",
      "type": "exit",
      "source": "platform"
    },
    {
      "name": "香港-210",
      "server": "91.245.253.*",
      "port": 4563,
      "protocol": "ss",
      "country_name": "HongKong",
      "country_code": "hk",
      "city_name": "HongKong",
      "delay": 124,
      "use_status": "none",
      "ingress_ip": "91.245.253.*",
      "ip": "**************",
      "ingress_country_name": "Singapore",
      "ingress_country_code": "sg",
      "ingress_city_name": "Singapore",
      "type": "exit",
      "source": "platform"
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» msg|string|true|none||none|
|» code|integer|true|none||none|
|» data|[object]|true|none|代理节点列表|同网关详细信息接口中的 proxies|
|»» name|string|true|none||none|
|»» server|string|true|none||none|
|»» port|integer|true|none||none|
|»» protocol|string|true|none||none|
|»» country_name|string|true|none||none|
|»» country_code|string|true|none||none|
|»» city_name|string|true|none||none|
|»» delay|integer|true|none||none|
|»» use_status|string|true|none||none|
|»» ingress_ip|string|true|none||none|
|»» ip|string|true|none||none|
|»» ingress_country_name|string|true|none||none|
|»» ingress_country_code|string|true|none||none|
|»» ingress_city_name|string|true|none||none|
|»» type|string|true|none||none|
|»» source|string|true|none||none|

## GET 检测节点延迟

GET /delay

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|proxy|query|string| 是 |none|

> 返回示例

```json
{
  "msg": "success",
  "code": 200,
  "data": {
    "delay": 90
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» msg|string|true|none||none|
|» code|integer|true|none||none|
|» data|object|true|none||none|
|»» delay|integer|true|none||none|

## GET 获取可打击部署的国家列表

GET /hit-deploy/country

> 返回示例

```json
{
  "msg": "success",
  "code": 200,
  "data": {
    "country": [
      {
        "code": "no",
        "name": "挪威"
      },
      {
        "code": "kr",
        "name": "韩国"
      },
      {
        "code": "si",
        "name": "斯洛文尼亚"
      },
      {
        "code": "tw",
        "name": "台湾"
      },
      {
        "code": "us",
        "name": "美国"
      },
      {
        "code": "fr",
        "name": "法国"
      },
      {
        "code": "gb",
        "name": "英国"
      },
      {
        "code": "pt",
        "name": "葡萄牙"
      },
      {
        "code": "hu",
        "name": "匈牙利"
      },
      {
        "code": "cz",
        "name": "捷克"
      },
      {
        "code": "se",
        "name": "瑞典"
      },
      {
        "code": "co",
        "name": "哥伦比亚"
      },
      {
        "code": "tr",
        "name": "土耳其"
      },
      {
        "code": "ro",
        "name": "罗马尼亚"
      },
      {
        "code": "rs",
        "name": "塞尔维亚"
      },
      {
        "code": "it",
        "name": "意大利"
      },
      {
        "code": "ru",
        "name": "俄罗斯联邦"
      },
      {
        "code": "bg",
        "name": "保加利亚"
      }
    ]
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» msg|string|true|none||none|
|» code|integer|true|none||none|
|» data|object|true|none||none|
|»» country|[object]|true|none||none|
|»»» code|string|true|none||none|
|»»» name|string|true|none||none|

## POST 打击部署节点

POST /hit-deploy

> Body 请求参数

```json
{
  "country_code": "kr",
  "vulnerability_type": "0day"
}
```

### 请求参数

| 名称                   | 位置   | 类型     | 必选 | 中文名    | 说明        |
|----------------------|------|--------|----|--------|-----------|
| body                 | body | object | 否  |        | none      |
| » country_code       | body | string | 是  | 国家代码   | none      |
| » vulnerability_type | body | string | 是  | 使用漏洞类型 | 0day、1day |

> 返回示例

```json
{
  "msg": "success",
  "code": 200
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» msg|string|true|none||none|
|» code|integer|true|none||none|

## GET 获取指定国家打击部署的节点列表

GET /api/hit-deploy

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|country_code|query|string| 是 ||none|

> 返回示例

```json
{
  "msg": "success",
  "code": 200,
  "data": [
    {
      "name": "韩国-牙山市-352",
      "server": "211.54.200.*",
      "port": 16580,
      "protocol": "socks5",
      "country_name": "South Korea",
      "country_code": "kr",
      "city_name": "Asan",
      "delay": -1,
      "use_status": "none",
      "ingress_ip": "211.54.200.*",
      "ip": "*************",
      "ingress_country_name": "South Korea",
      "ingress_country_code": "kr",
      "ingress_city_name": "Asan",
      "type": "exit",
      "source": "hit"
    },
    {
      "name": "韩国--371",
      "server": "211.63.190.*",
      "port": 16580,
      "protocol": "socks5",
      "country_name": "South Korea",
      "country_code": "kr",
      "city_name": "",
      "delay": -1,
      "use_status": "none",
      "ingress_ip": "211.63.190.*",
      "ip": "**************",
      "ingress_country_name": "South Korea",
      "ingress_country_code": "kr",
      "ingress_city_name": "",
      "type": "exit",
      "source": "hit"
    },
    {
      "name": "韩国-天安市-375",
      "server": "119.204.15.*",
      "port": 16580,
      "protocol": "socks5",
      "country_name": "South Korea",
      "country_code": "kr",
      "city_name": "Cheonan",
      "delay": -1,
      "use_status": "none",
      "ingress_ip": "119.204.15.*",
      "ip": "**************",
      "ingress_country_name": "South Korea",
      "ingress_country_code": "kr",
      "ingress_city_name": "Cheonan",
      "type": "exit",
      "source": "hit"
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» msg|string|true|none||none|
|» code|integer|true|none||none|
|» data|[object]|true|none||none|
|»» name|string|true|none||none|
|»» server|string|true|none||none|
|»» port|integer|true|none||none|
|»» protocol|string|true|none||none|
|»» country_name|string|true|none||none|
|»» country_code|string|true|none||none|
|»» city_name|string|true|none||none|
|»» delay|integer|true|none||none|
|»» use_status|string|true|none||none|
|»» ingress_ip|string|true|none||none|
|»» ip|string|true|none||none|
|»» ingress_country_name|string|true|none||none|
|»» ingress_country_code|string|true|none||none|
|»» ingress_city_name|string|true|none||none|
|»» type|string|true|none||none|
|»» source|string|true|none||none|

# 数据模型
