---
title: wuzhen-gateway
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.23"

---

# wuzhen-gateway

Base URLs:

# Authentication

- HTTP Authentication, scheme: bearer

# 链路

## GET IP与链路关联列表

GET /ipRuleList

> 返回示例

```json
{
  "msg": "success",
  "code": 200,
  "data": [
    {
      "ip": "*************",
      "proxy": "default(*************)-36fd",
      "wg": false
    },
    {
      "ip": "***********",
      "proxy": "default(*************)-36fd",
      "wg": false
    },
    {
      "ip": "************",
      "proxy": "default(*************)-36fd",
      "wg": false
    },
    {
      "ip": "***********",
      "proxy": "default(***********)-be59",
      "wg": false
    },
    {
      "ip": "***********",
      "proxy": "default(*************)-36fd",
      "wg": false
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» msg|string|true|none||none|
|» code|integer|true|none||none|
|» data|[object]|true|none||none|
|»» ip|string|true|none||none|
|»» proxy|string|true|none|代理节点或链路名称|none|
|»» wg|boolean|true|none|是否特殊模式|none|

## POST 切换关联的链路/节点

POST /ipRule

> Body 请求参数

```json
{
  "ip": "***********",
  "proxy": "加拿大-多伦多-191",
  "wg": false
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|body|body|object| 否 ||none|
|» ip|body|string| 是 ||none|
|» proxy|body|string| 是 | 链路/节点名称|如果传入链路名, 直接切换到指定链路, 如果传入节点名称, 会修改当前链路出口节点为指定节点|
|» wg|body|boolean| 是 | 是否开启特殊模式|none|

> 返回示例

```json
{
  "msg": "success",
  "code": 200
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» msg|string|true|none||none|
|» code|integer|true|none||none|

## GET 获取指定 IP 关联的链路

GET /ipRule

> Body 请求参数

```json
{
  "ip": "***********"
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|body|body|object| 否 ||none|
|» ip|body|string| 是 ||none|

> 返回示例

```json
{
  "msg": "success",
  "code": 200,
  "data": {
    "ip": "***********",
    "proxy": "default(*************)-36fd",
    "wg": false
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» msg|string|true|none||none|
|» code|integer|true|none||none|
|» data|object|true|none||none|
|»» ip|string|true|none||none|
|»» proxy|string|true|none||none|
|»» wg|boolean|true|none||none|

## GET 获取链路列表

GET /pathList

如果当前用户是管理员，会获取所有链路列表，非管理员只能拿到自己的链路列表

> 返回示例

```json
{
  "msg": "success",
  "code": 200,
  "data": [
    {
      "name": "default(*************)-36fd",
      "proxies": [
        "新加坡-137",
        "加拿大-多伦多-191"
      ],
      "use": true,
      "proxies_code": [
        "sg",
        "ca"
      ],
      "exclusive": "none",
      "account": "admin",
      "account_is_admin": true,
      "current_ip_use": true,
      "change_time": 0,
      "change_country_array": null
    },
    {
      "name": "default(***********)-be59",
      "proxies": [
        "香港-177",
        "孟加拉国-达卡-216"
      ],
      "use": true,
      "proxies_code": [
        "hk",
        "bd"
      ],
      "exclusive": "none",
      "account": "danbai",
      "account_is_admin": false,
      "current_ip_use": false,
      "change_time": 0,
      "change_country_array": null
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» msg|string|true|none||none|
|» code|integer|true|none||none|
|» data|[object]|true|none||none|
|»» name|string|true|none|链路名|none|
|»» proxies|[string]|true|none|链路中的代理节点|none|
|»» use|boolean|true|none|是否使用|none|
|»» proxies_code|[string]|true|none|代理节点国家代码列表|none|
|»» exclusive|string|true|none|独占|none|
|»» account|string|true|none|所属用户|none|
|»» account_is_admin|boolean|true|none|用户是否为管理员|none|
|»» current_ip_use|boolean|true|none|是否当前IP使用的链路|none|
|»» change_time|integer|true|none|跳变间隔时间|秒为单位|
|»» change_country_array|[string]¦null|false|none|跳变国家列表|none|

## POST 创建/更新链路

POST /path

> Body 请求参数

```json
{
  "name": "test_path_name",
  "proxies": [
    "香港-177",
    "加拿大-多伦多-191"
  ],
  "exclusive": "none",
  "change_time": 0,
  "change_country_array": [
    [
      "sg",
      "jp"
    ],
    [
      "us"
    ]
  ],
  "new_name": "new_test_path_name",
  "create": true,
  "apply": false
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|body|body|object| 否 ||none|
|» name|body|string| 是 | 链路名称|none|
|» proxies|body|[string]| 是 | 链路中节点名|none|
|» exclusive|body|string| 是 | 独占|none/out 只独占出口节点/all 独占所有节点|
|» change_time|body|integer| 否 | 跳变间隔时间|秒为单位, 0为不开启跳变|
|» change_country_array|body|[array]| 否 | 跳变国家范围|none|
|» new_name|body|string| 否 | 新链路名称|更新时传|
|» create|body|boolean| 否 | 是否为创建|none|
|» apply|body|boolean| 否 | 是否应用|是否在创建/更新后使用此链路|

> 返回示例

```json
{
  "msg": "success",
  "code": 200
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» msg|string|true|none||none|
|» code|integer|true|none||none|

## DELETE 删除链路

DELETE /path

> Body 请求参数

```json
{
  "name": "test_path_name"
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|body|body|object| 否 ||none|
|» name|body|string| 是 | 链路名|none|

> 返回示例

```json
{
  "msg": "success",
  "code": 200
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» msg|string|true|none||none|
|» code|integer|true|none||none|

# 数据模型
