---
title: wuzhen-gateway
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.23"

---

# wuzhen-gateway

Base URLs:

# Authentication

- HTTP Authentication, scheme: bearer

# 用户

## POST 登录

POST /login

> Body 请求参数

```json
{
  "account": "admin",
  "password": "1d22d82c901358e1d3239adb3abc480fb784266f6f0d8ebee152c29a4df67b96"
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|body|body|object| 否 ||none|
|» account|body|string| 是 | 账号|none|
|» password|body|string| 是 | 密码|需要加密, 加密方式为fmt.Sprintf("%x", sha256.Sum256([]byte(fmt.Sprintf("pass-%s", password))))|

> 返回示例

```json
{
  "msg": "success",
  "code": 200,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************.5VSi9QHiQHYZlicQrzpOx0uWlwv95qezTYi8cwhuPgE",
    "expire_time": "2024-10-13T11:00:17+08:00"
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» msg|string|true|none||none|
|» code|integer|true|none||none|
|» data|object|true|none||none|
|»» token|string|true|none||none|
|»» expire_time|string|true|none||none|

## POST 注册

POST /register

> Body 请求参数

```json
{
  "account": "test",
  "password": "1d22d82c901358e1d3239adb3abc480fb784266f6f0d8ebee152c29a4df67b96"
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|body|body|object| 否 ||none|
|» account|body|string| 是 | 账号|账号需不小于4个字符, 并且只能有英文和数字组成|
|» password|body|string| 是 | 密码|需要加密, 加密方式为fmt.Sprintf("%x", sha256.Sum256([]byte(fmt.Sprintf("pass-%s", password))))|

> 返回示例

```json
{
  "msg": "success",
  "code": 200,
  "data": {
    "id": "03bdec4f-3f45-416b-9d94-d1c3d77bf673"
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» msg|string|true|none||none|
|» code|integer|true|none||none|
|» data|object|true|none||none|
|»» id|string|true|none||none|

## GET 获取当前登录用户信息

GET /user/info

> 返回示例

```json
{
  "msg": "success",
  "code": 200,
  "data": {
    "id": "9e41aaa1-733c-464f-80fe-a0c1c0292e8f",
    "account": "admin",
    "is_admin": true,
    "is_active": true,
    "created_at": "2024-10-09T10:23:22+08:00",
    "last_login_at": "2024-10-12T14:39:45+08:00"
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» msg|string|true|none||none|
|» code|integer|true|none||none|
|» data|object|true|none||none|
|»» id|string|true|none||none|
|»» account|string|true|none||none|
|»» is_admin|boolean|true|none||none|
|»» is_active|boolean|true|none||none|
|»» created_at|string|true|none||none|
|»» last_login_at|string|true|none||none|

## PATCH 修改登录用户密码

PATCH /user/password

> Body 请求参数

```json
{
  "password": "1d22d82c901358e1d3239adb3abc480fb784266f6f0d8ebee152c29a4df67b96"
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|body|body|object| 否 ||none|
|» password|body|string| 是 | 密码|需要加密, 加密方式为fmt.Sprintf("%x", sha256.Sum256([]byte(fmt.Sprintf("pass-%s", password))))|

> 返回示例

```json
{
  "msg": "success",
  "code": 200,
  "data": {
    "id": "9e41aaa1-733c-464f-80fe-a0c1c0292e8f"
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» msg|string|true|none||none|
|» code|integer|true|none||none|
|» data|object|true|none||none|
|»» id|string|true|none||none|

## POST 管理员创建用户

POST /user

> Body 请求参数

```json
{
  "account": "test_create",
  "password": "1d22d82c901358e1d3239adb3abc480fb784266f6f0d8ebee152c29a4df67b96"
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|body|body|object| 否 ||none|
|» account|body|string| 是 ||none|
|» password|body|string| 是 ||需要加密, 加密方式为fmt.Sprintf("%x", sha256.Sum256([]byte(fmt.Sprintf("pass-%s", password))))|

> 返回示例

```json
{
  "msg": "success",
  "code": 200,
  "data": {
    "id": "52a94012-e80e-490e-aea3-f90ca7df48bf"
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» msg|string|true|none||none|
|» code|integer|true|none||none|
|» data|object|true|none||none|
|»» id|string|true|none||none|

## DELETE 管理员删除用户

DELETE /user/{uuid}

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|uuid|path|string| 是 ||none|

> 返回示例

```json
{
  "msg": "success",
  "code": 200,
  "data": [
    {
      "id": "52a94012-e80e-490e-aea3-f90ca7df48bf"
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» msg|string|true|none||none|
|» code|integer|true|none||none|
|» data|[object]|true|none||none|
|»» id|string|false|none||none|

## GET 管理员获取用户列表

GET /user/list

> 返回示例

```json
{
  "msg": "success",
  "code": 200,
  "data": [
    {
      "id": "03bdec4f-3f45-416b-9d94-d1c3d77bf673",
      "account": "test",
      "is_admin": false,
      "is_active": true,
      "created_at": "2024-10-12T11:08:53+08:00",
      "last_login_at": "1970-01-01T08:00:00+08:00"
    },
    {
      "id": "124e647c-d2ea-451c-8467-9d392c10a9c2",
      "account": "danbai",
      "is_admin": false,
      "is_active": true,
      "created_at": "2024-10-12T10:00:42+08:00",
      "last_login_at": "2024-10-12T10:00:47+08:00"
    },
    {
      "id": "9e41aaa1-733c-464f-80fe-a0c1c0292e8f",
      "account": "admin",
      "is_admin": true,
      "is_active": true,
      "created_at": "2024-10-09T10:23:22+08:00",
      "last_login_at": "2024-10-12T14:39:45+08:00"
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» msg|string|true|none||none|
|» code|integer|true|none||none|
|» data|[object]|true|none||none|
|»» id|string|true|none||none|
|»» account|string|true|none||none|
|»» is_admin|boolean|true|none||none|
|»» is_active|boolean|true|none||none|
|»» created_at|string|true|none||none|
|»» last_login_at|string|true|none||none|

## PATCH 管理员修改指定用户密码

PATCH /user/password/{uuid}

> Body 请求参数

```json
{
  "password": "1d22d82c901358e1d3239adb3abc480fb784266f6f0d8ebee152c29a4df67b96"
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|uuid|path|string| 是 ||none|
|body|body|object| 否 ||none|
|» password|body|string| 是 ||none|

> 返回示例

```json
{
  "msg": "success",
  "code": 200,
  "data": {
    "id": "4cec390c-105c-448b-9928-e11587ed0d86"
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» msg|string|true|none||none|
|» code|integer|true|none||none|
|» data|object|true|none||none|
|»» id|string|true|none||none|

# 数据模型
