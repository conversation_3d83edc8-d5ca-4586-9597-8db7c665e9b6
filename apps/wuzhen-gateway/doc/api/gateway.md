---
title: wuzhen-gateway
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.23"

---

# wuzhen-gateway

Base URLs:

# Authentication

- HTTP Authentication, scheme: bearer

# 网关信息

## GET 网关状态

GET /status

> 返回示例

```json
{
  "msg": "success",
  "code": 200,
  "data": {
    "status": "ok"
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» msg|string|true|none||none|
|» code|integer|true|none||none|
|» data|object|true|none||none|
|»» status|string|true|none|状态|wan为异常 load为加载中 ok为正常|

## GET 详细信息

GET /info

> 返回示例

```json
{
  "msg": "success",
  "code": 200,
  "data": {
    "proxy_info": {
      "name": "default(*************)-36fd",
      "proxies": [
        {
          "name": "新加坡-137",
          "server": "152.42.226.*",
          "port": 4560,
          "protocol": "ss",
          "country_name": "Singapore",
          "country_code": "sg",
          "city_name": "Singapore",
          "delay": 87,
          "use_status": "use",
          "ingress_ip": "152.42.226.*",
          "ip": "************",
          "ingress_country_name": "Singapore",
          "ingress_country_code": "sg",
          "ingress_city_name": "Singapore",
          "type": "guard",
          "source": "platform"
        },
        {
          "name": "日本-东京-211",
          "server": "91.245.253.*",
          "port": 4564,
          "protocol": "ss",
          "country_name": "Japan",
          "country_code": "jp",
          "city_name": "Tokyo",
          "delay": 160,
          "use_status": "use",
          "ingress_ip": "91.245.253.*",
          "ip": "*************",
          "ingress_country_name": "Singapore",
          "ingress_country_code": "sg",
          "ingress_city_name": "Singapore",
          "type": "exit",
          "source": "platform"
        }
      ],
      "exclusive": "none",
      "wg": false,
      "change_time": 0,
      "change_at": 1728528378
    },
    "device_information": {
      "network_status": "up",
      "ip": "**********45",
      "you_ip": "***********",
      "mac": "9e:f8:9f:67:a2:ba",
      "gateway": "**********",
      "uptime": 2574,
      "interface_name": "eth0"
    },
    "interface_information": [
      {
        "type": "wan",
        "interface": "eth0",
        "status": "up",
        "speed": 1000
      },
      {
        "type": "lan",
        "interface": "eth1",
        "status": "down",
        "speed": -1
      },
      {
        "type": "lan",
        "interface": "eth2",
        "status": "down",
        "speed": -1
      },
      {
        "type": "wan",
        "interface": "wlan0",
        "status": "up",
        "speed": -1
      },
      {
        "type": "lan",
        "interface": "wlan1",
        "status": "up",
        "speed": 0
      },
      {
        "type": "wan",
        "interface": "wwan0",
        "status": "up",
        "speed": -1
      }
    ],
    "devices_num": 2,
    "historical_traffic": {
      "last_7_days": [
        {
          "Date": "2024-10-12",
          "Up": 2371419,
          "Down": 103109567,
          "CreatedAt": 1728662400,
          "UpdatedAt": 1728702904
        },
        {
          "Date": "2024-10-11",
          "Up": 5953354,
          "Down": 188564322,
          "CreatedAt": 1728576000,
          "UpdatedAt": 1728662395
        },
        {
          "Date": "2024-10-10",
          "Up": 131798660,
          "Down": 399330601,
          "CreatedAt": 1728489604,
          "UpdatedAt": 1728575995
        },
        {
          "Date": "2024-10-09",
          "Up": 3053568,
          "Down": 42677626,
          "CreatedAt": 1728440641,
          "UpdatedAt": 1728489599
        }
      ],
      "last_30_days": [
        {
          "Date": "2024-10-12",
          "Up": 2371419,
          "Down": 103109567,
          "CreatedAt": 1728662400,
          "UpdatedAt": 1728702904
        },
        {
          "Date": "2024-10-11",
          "Up": 5953354,
          "Down": 188564322,
          "CreatedAt": 1728576000,
          "UpdatedAt": 1728662395
        },
        {
          "Date": "2024-10-10",
          "Up": 131798660,
          "Down": 399330601,
          "CreatedAt": 1728489604,
          "UpdatedAt": 1728575995
        },
        {
          "Date": "2024-10-09",
          "Up": 3053568,
          "Down": 42677626,
          "CreatedAt": 1728440641,
          "UpdatedAt": 1728489599
        }
      ],
      "last_180_days": [
        {
          "Date": "2024-10-12",
          "Up": 2371419,
          "Down": 103109567,
          "CreatedAt": 1728662400,
          "UpdatedAt": 1728702904
        },
        {
          "Date": "2024-10-11",
          "Up": 5953354,
          "Down": 188564322,
          "CreatedAt": 1728576000,
          "UpdatedAt": 1728662395
        },
        {
          "Date": "2024-10-10",
          "Up": 131798660,
          "Down": 399330601,
          "CreatedAt": 1728489604,
          "UpdatedAt": 1728575995
        },
        {
          "Date": "2024-10-09",
          "Up": 3053568,
          "Down": 42677626,
          "CreatedAt": 1728440641,
          "UpdatedAt": 1728489599
        }
      ]
    },
    "dhcp": true
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» msg|string|true|none||none|
|» code|integer|true|none||none|
|» data|object|true|none||none|
|»» proxy_info|object|true|none|代理链路信息|none|
|»»» name|string|true|none|链路名|none|
|»»» proxies|[object]|true|none|代理节点列表|none|
|»»»» name|string|true|none|代理节点名|none|
|»»»» server|string|true|none||none|
|»»»» port|integer|true|none||none|
|»»»» protocol|string|true|none|代理协议|none|
|»»»» country_name|string|true|none||none|
|»»»» country_code|string|true|none||none|
|»»»» city_name|string|true|none||none|
|»»»» delay|integer|true|none|延迟|none|
|»»»» use_status|string|true|none|使用状态|none|
|»»»» ingress_ip|string|true|none|入口 VPS IP|none|
|»»»» ip|string|true|none|出口 IP|none|
|»»»» ingress_country_name|string|true|none||none|
|»»»» ingress_country_code|string|true|none||none|
|»»»» ingress_city_name|string|true|none||none|
|»»»» type|string|true|none|类型|guard/exit|
|»»»» source|string|true|none|来源|platform/hit/imported|
|»»» exclusive|string|true|none|独占|none/out 只独占出口节点/all 独占所有节点|
|»»» wg|boolean|true|none|是否特殊模式|none|
|»»» change_time|integer|true|none|跳变间隔时间|秒为单位, 0为未开启跳变|
|»»» change_at|integer|true|none|更新时间|none|
|»» device_information|object|true|none|设备网络信息|none|
|»»» network_status|string|true|none|网络状态|up/down|
|»»» ip|string|true|none|网关所在 IP|none|
|»»» you_ip|string|true|none|当前用户所在 IP|none|
|»»» mac|string|true|none|网关 MAC 地址|none|
|»»» gateway|string|true|none|上游网关 IP|none|
|»»» uptime|integer|true|none|网络正常运行时间|秒为单位|
|»»» interface_name|string|true|none|网络接口名称|none|
|»» interface_information|[object]|true|none|网络接口信息|none|
|»»» type|string|true|none|类型|wan/lan|
|»»» interface|string|true|none|接口名称|none|
|»»» status|string|true|none|状态|up/down|
|»»» speed|integer|true|none|协商速率|none|
|»» devices_num|integer|true|none|在线设备数量|none|
|»» historical_traffic|object|true|none|历史流量信息|none|
|»»» last_7_days|[object]|true|none||none|
|»»»» Date|string|true|none||none|
|»»»» Up|integer|true|none||none|
|»»»» Down|integer|true|none||none|
|»»»» CreatedAt|integer|true|none||none|
|»»»» UpdatedAt|integer|true|none||none|
|»»» last_30_days|[object]|true|none||none|
|»»»» Date|string|true|none||none|
|»»»» Up|integer|true|none||none|
|»»»» Down|integer|true|none||none|
|»»»» CreatedAt|integer|true|none||none|
|»»»» UpdatedAt|integer|true|none||none|
|»»» last_180_days|[object]|true|none||none|
|»»»» Date|string|true|none||none|
|»»»» Up|integer|true|none||none|
|»»»» Down|integer|true|none||none|
|»»»» CreatedAt|integer|true|none||none|
|»»»» UpdatedAt|integer|true|none||none|
|»» dhcp|boolean|true|none|是否开启 DHCP|none|

## GET 管理员获取连接设备列表

GET /clientList

> 返回示例

```json
{
  "msg": "success",
  "code": 200,
  "data": [
    {
      "Mac": "c2:3f:de:a0:64:b0",
      "Hostname": "",
      "IP": "*************",
      "LeaseEndTime": 1729045586,
      "Static": false,
      "CreatedAt": 1728440784,
      "UpdatedAt": 1728440786,
      "proxy": "default(*************)-36fd",
      "active": false
    },
    {
      "Mac": "36:a0:cb:74:95:16",
      "Hostname": "Mac",
      "IP": "***********",
      "LeaseEndTime": 1729132688,
      "Static": false,
      "CreatedAt": 1728461268,
      "UpdatedAt": 1728527888,
      "proxy": "default(*************)-36fd",
      "active": false
    },
    {
      "Mac": "d2:10:44:80:e6:5d",
      "Hostname": "iPhone",
      "IP": "*************",
      "LeaseEndTime": 1729302619,
      "Static": false,
      "CreatedAt": 1728626654,
      "UpdatedAt": 1728697819,
      "proxy": "auto",
      "active": false
    },
    {
      "Mac": "ea:91:13:59:46:7d",
      "Hostname": "Unknown",
      "IP": "************",
      "LeaseEndTime": 1729302937,
      "Static": true,
      "CreatedAt": 1728698137,
      "UpdatedAt": 1728698137,
      "proxy": "default(*************)-36fd",
      "active": true
    },
    {
      "Mac": "62:95:94:7d:6a:28",
      "Hostname": "Unknown",
      "IP": "***********",
      "LeaseEndTime": 1729303247,
      "Static": true,
      "CreatedAt": 1728698447,
      "UpdatedAt": 1728698447,
      "proxy": "default(***********)-be59",
      "active": true
    },
    {
      "Mac": "00:e0:4c:76:e5:2a",
      "Hostname": "Unknown",
      "IP": "***********",
      "LeaseEndTime": 1729307707,
      "Static": true,
      "CreatedAt": 1728702907,
      "UpdatedAt": 1728702907,
      "proxy": "default(*************)-36fd",
      "active": false
    },
    {
      "Mac": "3c:a6:f6:60:8f:70",
      "Hostname": "Unknown",
      "IP": "************",
      "LeaseEndTime": 1729319985,
      "Static": true,
      "CreatedAt": 1728715185,
      "UpdatedAt": 1728715185,
      "proxy": "default(*************)-36fd",
      "active": true
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» msg|string|true|none||none|
|» code|integer|true|none||none|
|» data|[object]|true|none||none|
|»» Mac|string|true|none||none|
|»» Hostname|string|true|none||none|
|»» IP|string|true|none||none|
|»» LeaseEndTime|integer|true|none||none|
|»» Static|boolean|true|none||none|
|»» CreatedAt|integer|true|none||none|
|»» UpdatedAt|integer|true|none||none|
|»» proxy|string|true|none||none|
|»» active|boolean|true|none||none|

# 数据模型
