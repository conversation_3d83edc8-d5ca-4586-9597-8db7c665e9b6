include(CMakeDependentOption)
cmake_dependent_option(LPAC_DYNAMIC_DRIVERS "Build lpac/libeuicc driver backends as a dynamic library" OFF "LPAC_DYNAMIC_LIBEUICC" OFF)

option(LPAC_WITH_APDU_PCSC "Build APDU PCSC Backend (requires PCSC libraries)" ON)
option(LPAC_WITH_APDU_AT "Build APDU AT Backend" ON)
option(LPAC_WITH_APDU_GBINDER "Build APDU Gbinder backend for libhybris devices (requires gbinder headers)" OFF)
option(LPAC_WITH_APDU_QMI_QRTR "Build QMI-over-QRTR backend for Qualcomm devices (requires libqrtr and libqmi headers)" OFF)

option(LPAC_WITH_HTTP_CURL "Build HTTP Curl interface" ON)

aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR} DIR_INTERFACE_SRCS)
if(LPAC_DYNAMIC_DRIVERS)
    add_library(euicc-drivers SHARED ${DIR_INTERFACE_SRCS})
    list(APPEND LIBEUICC_DRIVERS_REQUIRES "libeuicc = ${PROJECT_VERSION}")
else()
    add_library(euicc-drivers STATIC ${DIR_INTERFACE_SRCS})
endif()
target_link_libraries(euicc-drivers euicc cjson-static)
target_include_directories(euicc-drivers PUBLIC ${CMAKE_CURRENT_SOURCE_DIR})

target_sources(euicc-drivers PRIVATE ${CMAKE_CURRENT_SOURCE_DIR}/apdu/stdio.c)
target_sources(euicc-drivers PRIVATE ${CMAKE_CURRENT_SOURCE_DIR}/http/stdio.c)

if(LPAC_WITH_APDU_PCSC)
    set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -DLPAC_WITH_APDU_PCSC")
    target_sources(euicc-drivers PRIVATE ${CMAKE_CURRENT_SOURCE_DIR}/apdu/pcsc.c)
    if(WIN32)
        target_link_libraries(euicc-drivers winscard)
    elseif(APPLE)
        target_link_libraries(euicc-drivers "-framework PCSC")
        if(LPAC_DYNAMIC_DRIVERS)
            # for pkg-config
            set(LIBEUICC_DRIVERS_EXTRA_CFLAGS "-framework PCSC")
        endif()
    else()
        find_package(PCSCLite)
        target_link_libraries(euicc-drivers PCSCLite::PCSCLite)
        if(LPAC_DYNAMIC_DRIVERS)
            list(APPEND LIBEUICC_DRIVERS_REQUIRES "libpcsclite")
        endif()
    endif()
endif()

if(LPAC_WITH_APDU_AT)
    set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -DLPAC_WITH_APDU_AT")
    target_sources(euicc-drivers PRIVATE ${CMAKE_CURRENT_SOURCE_DIR}/apdu/at.c)
endif()

if(LPAC_WITH_APDU_GBINDER)
    set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -DLPAC_WITH_APDU_GBINDER")
    target_sources(euicc-drivers PRIVATE ${CMAKE_CURRENT_SOURCE_DIR}/apdu/gbinder_hidl.c)
    find_package(PkgConfig REQUIRED)
    pkg_check_modules(GBINDER REQUIRED IMPORTED_TARGET libgbinder)
    pkg_check_modules(GLIB REQUIRED IMPORTED_TARGET glib-2.0)
    target_link_libraries(euicc-drivers PkgConfig::GBINDER PkgConfig::GLIB)
    if(LPAC_DYNAMIC_DRIVERS)
        list(APPEND LIBEUICC_DRIVERS_REQUIRES "libgbinder")
        list(APPEND LIBEUICC_DRIVERS_REQUIRES "glib-2.0")
    endif()
endif()

if(LPAC_WITH_APDU_QMI_QRTR)
    set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -DLPAC_WITH_APDU_QMI_QRTR")
    target_sources(euicc-drivers PRIVATE ${CMAKE_CURRENT_SOURCE_DIR}/apdu/qmi_qrtr.c ${CMAKE_CURRENT_SOURCE_DIR}/apdu/qmi_qrtr_helpers.c)
    find_package(PkgConfig REQUIRED)
    pkg_check_modules(QRTR_GLIB REQUIRED IMPORTED_TARGET qrtr-glib)
    pkg_check_modules(QMI_GLIB REQUIRED IMPORTED_TARGET qmi-glib)
    target_link_libraries(euicc-drivers PkgConfig::QRTR_GLIB PkgConfig::QMI_GLIB)
    if(LPAC_DYNAMIC_DRIVERS)
        list(APPEND LIBEUICC_DRIVERS_REQUIRES "qrtr-glib")
        list(APPEND LIBEUICC_DRIVERS_REQUIRES "qmi-glib")
    endif()
endif()

if(LPAC_WITH_HTTP_CURL)
    set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -DLPAC_WITH_HTTP_CURL")
    target_sources(euicc-drivers PRIVATE ${CMAKE_CURRENT_SOURCE_DIR}/http/curl.c)
    if(WIN32)
        target_link_libraries(euicc-drivers ${DL_LIBRARY})
    else()
        find_package(CURL REQUIRED)
        target_link_libraries(euicc-drivers curl)
        if(LPAC_DYNAMIC_DRIVERS)
            list(APPEND LIBEUICC_DRIVERS_REQUIRES "libcurl")
        endif()
    endif()
endif()

if(LPAC_DYNAMIC_DRIVERS)
    # Install headers
    file(GLOB ALL_HEADERS "*.h")
    foreach(header ${ALL_HEADERS})
        if(${header} MATCHES "^.*\.private\.h$")
            list(REMOVE_ITEM ALL_HEADERS ${header})
        endif()
    endforeach()
    set_target_properties(euicc-drivers PROPERTIES PUBLIC_HEADER "${ALL_HEADERS}")
    # Install a pkg-config file (mainly for Linux; macOS is untested; Win32 is not supported)
    if(UNIX)
        list(JOIN LIBEUICC_DRIVERS_REQUIRES ", " LIBEUICC_DRIVERS_REQUIRES)
        configure_file(libeuicc-drivers.pc.in libeuicc-drivers.pc @ONLY)
        install(FILES ${CMAKE_CURRENT_BINARY_DIR}/libeuicc-drivers.pc DESTINATION ${CMAKE_INSTALL_LIBDIR}/pkgconfig)
    endif()
    set_target_properties(euicc-drivers PROPERTIES SOVERSION ${PROJECT_VERSION_MAJOR})
    install(TARGETS euicc-drivers LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
                          PUBLIC_HEADER DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}/euicc)
endif()
