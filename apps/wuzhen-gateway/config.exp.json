{"dev": false, "device_code": "TT24C8ANHCTT", "secret": "31c0df46-2a65-412f-81f4-d0f74bdcfc37", "log_level": "info", "pprof": false, "jwt_secret": "", "clash_log_level": "silent", "clash_api_address": "127.0.0.1:5124", "clash_secret": "xrouter", "clash_proxy_port": 5123, "cloud_gateway": "http://*************:8080", "clash_file_path": "clash.yaml", "test_delay_url": "http://www.gstatic.com/generate_204", "test_delay_timeout": 10000, "customization_rule": ["IP-CIDR,10.0.0.0/8,DIRECT"], "export": "http://gateway:8080/export", "export_log": "https://vnet3.enicfg.com/loki/api/v1/push", "wg_endpoint": "***********:123", "customization_hosts": {"x.net": "**********"}, "dsn": "http://6e2333b800266996fda6392db796bcd2@*************:9000/9", "exclusive_num": 3, "dhcp": {"enable": true, "lease": 604800, "if_name": "br-lan"}, "pass_gfw_list": [{"name": "hk", "type": "ss", "server": "xxx", "password": "xxx", "port": 10000, "cipher": "aes-128-gcm", "country_name": "HongKong", "country_code": "hk", "city_name": "HongKong", "ip": "*******", "ingress_ip": "*******", "ingress_country_name": "HongKong", "ingress_country_code": "jp"}], "guard_list": [{"name": "jp", "type": "ss", "server": "xxx", "password": "xxx", "port": 10000, "cipher": "aes-128-gcm", "country_name": "Japan", "country_code": "jp", "city_name": "Tokyo", "ip": "*******", "ingress_ip": "*******", "ingress_country_name": "Tokyo", "ingress_country_code": "jp"}], "exit_list": [{"name": "jp2", "type": "ss", "server": "xxx", "password": "xxx", "port": 10000, "cipher": "aes-128-gcm", "country_name": "Japan", "country_code": "jp", "city_name": "Tokyo", "ingress_ip": "*******", "ingress_country_name": "Tokyo", "ingress_country_code": "jp"}, {"name": "jp3", "type": "ss", "server": "xxx", "password": "xxx", "port": 10000, "cipher": "aes-128-gcm", "country_name": "Japan", "country_code": "jp", "city_name": "Tokyo", "ingress_ip": "", "ingress_country_name": "", "ingress_country_code": "", "ingress_city_name": ""}]}