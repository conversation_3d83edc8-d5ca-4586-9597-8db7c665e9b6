FROM golang:1.24.5-alpine3.22 AS builder
WORKDIR /builder
COPY . .

RUN go mod download

RUN --mount=type=cache,target=/go/pkg/mod --mount=type=cache,target=/root/.cache/go-build \
    CGO_ENABLED=0 go build -trimpath -ldflags "-extldflags '-static' -s -w -X gateway/internal/config.BuildTime=1752139436 -X gateway/internal/config.Version=v1.1.25-gateway" -o build/gateway gateway/cmd/gateway

FROM alpine:3.22
WORKDIR /app
COPY --from=builder /builder/build/gateway /app/gateway
CMD ["/app/gateway"]
