package status

import (
	"gateway/internal/db"
	"gateway/internal/log"
	"gateway/internal/model"
	"gateway/internal/utils"
	"time"

	"github.com/metacubex/mihomo/tunnel/statistic"
)

func startClient() {
	getDb := db.GetDb()
	for {
		unix := time.Now().Unix()
		tx := getDb.Exec(
			"DELETE FROM ip_rule WHERE ip IN (SELECT ip FROM client WHERE lease_end_time > -1 AND lease_end_time < ?)",
			unix,
		)
		if tx.RowsAffected > 0 {
			getDb.Exec("DELETE FROM client WHERE lease_end_time > -1 AND lease_end_time < ?", unix)
			model.CheckPathUse(getDb)
			model.CheckProxiesUse(getDb)
			log.GetLogger().Infof("clean up expired clients deleted:%d", tx.RowsAffected)
		}
		clients := make([]model.Client, 0)
		getDb.Model(&model.Client{}).Find(&clients)
		t := statistic.DefaultManager
		connections := t.Snapshot().Connections
		conMap := make(map[string]struct{})
		for _, connection := range connections {
			conMap[connection.Metadata.SrcIP.String()] = struct{}{}
		}
		for _, entry := range clients {
			go func(ip string) {
				ping := utils.Ping(ip)
				_, has := conMap[ip]
				if ping >= 0 || has {
					activeIpMap.Store(ip, struct{}{})
				} else {
					activeIpMap.Delete(ip)
				}
			}(entry.IP)
		}
		time.Sleep(time.Minute)
	}
}
