package status

import (
	"bytes"
	"compress/gzip"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"gateway/internal/config"
	"gateway/internal/db"
	"gateway/internal/log"
	"gateway/internal/model"
	"net/http"
	"sync"
	"time"

	"github.com/google/uuid"
	"github.com/metacubex/mihomo/common/atomic"
)

func startExportLog() {
	if config.Config().ExportLog != "" {
		exporter := newLogExporter()
		exporter.Start()
	}
}

type logExporter struct {
	// 待上传的数据包，一个数据包中有多条日志
	reportChan chan *bytes.Buffer
	// 上报数据时间间隔
	reportInterval time.Duration
	// 日志事件缓冲池，处理好的日志在这里等待上报，满100条或者到达上报数据时间间隔上报
	events [][]any
	// 这个锁是用来保证 events flush时没有新的 event 写入
	lock sync.Mutex

	client *http.Client

	pause     *atomic.Bool
	failCount *atomic.Uint32
}

func newLogExporter() *logExporter {
	pause := atomic.NewBool(false)
	failCount := atomic.NewUint32(0)
	return &logExporter{
		reportChan:     make(chan *bytes.Buffer, 100),
		reportInterval: time.Second * 15,
		events:         make([][]any, 0),
		client:         &http.Client{Timeout: time.Second * 10},
		pause:          &pause,
		failCount:      &failCount,
	}
}

func (l *logExporter) Start() {
	go l.subscribeLog()
	go l.reportProcess()
	ticker := time.NewTicker(l.reportInterval)
	for range ticker.C {
		// 如果暂停，则跳过
		if l.pause.Load() {
			continue
		}
		// 定时上报
		go func() {
			// 从数据库中获取缓存的日志
			logs := make([]model.CacheLog, 0)
			db.GetDb().Model(&model.CacheLog{}).Limit(100).Order("created_at").Find(&logs)
			for _, logRecord := range logs {
				l.reportChan <- bytes.NewBuffer(logRecord.Data)
			}
		}()
		if len(l.events) > 0 {
			l.flushEvents()
		}
	}
}

// subscribeLog 订阅程序日志，并把日志写入到reportChan中
func (l *logExporter) subscribeLog() {
	subscribe := log.Subscribe("loki")
	for event := range subscribe {
		// 如果暂停，则跳过，丢弃event
		if l.pause.Load() {
			continue
		}

		// 处理日志为上报的格式
		fields := map[string]string{
			"caller": fmt.Sprintf("%s:%d", event.Caller.Function, event.Caller.Line),
			"level":  event.Level.String(),
		}
		if len(event.Data) > 0 {
			for k, v := range event.Data {
				fields[k] = fmt.Sprintf("%v", v)
			}
		}

		l.lock.Lock()
		l.events = append(l.events, []interface{}{
			fmt.Sprintf("%d", event.Time.UnixNano()),
			event.Message,
			fields,
		})
		l.lock.Unlock()

		if len(l.events) >= 100 {
			l.flushEvents()
		}
	}
}

// reportProcess 监听reportChan，当有数据时，发送到loki
func (l *logExporter) reportProcess() {
	encodedAuth := ""
	if config.Config().ExportLogBasicAuth != nil {
		// 如果设置了BasicAuth
		username := config.Config().ExportLogBasicAuth.UserName
		password := config.Config().ExportLogBasicAuth.Password
		auth := username + ":" + password
		encodedAuth = base64.StdEncoding.EncodeToString([]byte(auth))
	}

	for buffer := range l.reportChan {
		go l.sendToLoki(buffer, encodedAuth)
	}
}

func (l *logExporter) sendToLoki(buffer *bytes.Buffer, encodedAuth string) {
	var buf bytes.Buffer
	gzipWriter := gzip.NewWriter(&buf)
	_, _ = gzipWriter.Write(buffer.Bytes())
	_ = gzipWriter.Close()
	request, _ := http.NewRequest("POST", config.Config().ExportLog, &buf)
	request.Header.Set("Content-Encoding", "gzip")
	request.Header.Set("Content-Type", "application/json")
	if encodedAuth != "" {
		request.Header.Add("Authorization", "Basic "+encodedAuth)
	}
	resp, err := l.client.Do(request)
	if err != nil {
		l.failCount.Add(1)
		log.GetLogger().Infof("log export failed count: %d", l.failCount.Load())
		// 如果失败次数超过100次，则暂停上报
		// 100 条日志数据包没有成功上报，大概等于 10000 条日志或者 25 分钟没有上报成功
		if l.failCount.Load() > 100 {
			if l.pause.CompareAndSwap(false, true) {
				log.GetLogger().Warn("log exporter pause")
			}
			go l.statusWatch() // 启动状态观察，在网络恢复后解除暂停
		}
		id := uuid.NewSHA1(uuid.Nil, buffer.Bytes())
		db.GetDb().
			Model(&model.CacheLog{}).
			Where("id = ?", id.String()).
			FirstOrCreate(&model.CacheLog{
				ID:   uuid.NewSHA1(uuid.Nil, buffer.Bytes()),
				Data: buffer.Bytes(),
			})
	} else {
		l.failCount.Store(0)
		_ = resp.Body.Close()
		db.GetDb().Delete(&model.CacheLog{}, "id = ?", uuid.NewSHA1(uuid.Nil, buffer.Bytes()))
	}
}

func (l *logExporter) flushEvents() {
	l.lock.Lock()
	defer l.lock.Unlock()
	l.reportChan <- bytes.NewBuffer(newLogData(l.events))
	l.events = make([][]interface{}, 0)
}

func (l *logExporter) statusWatch() {
	ticker := time.NewTicker(time.Minute)
	defer ticker.Stop()
	for range ticker.C {
		request, _ := http.NewRequest("GET", config.Config().ExportLog, nil)
		_, err := l.client.Do(request)
		if err != nil {
			continue
		}
		l.failCount.Store(0)
		if l.pause.CompareAndSwap(true, false) {
			log.GetLogger().Infof("status watch: recover log exporter")
		}

		return
	}

}

type logData struct {
	Streams []struct {
		logDataStream `json:"stream"`
		Values        [][]interface{} `json:"values"`
	} `json:"streams"`
}
type logDataStream struct {
	Job         string `json:"job"`
	DeviceCode  string `json:"device_code"`
	ServiceName string `json:"service_name"`
	Env         string `json:"env"`
}

func newLogData(logs [][]interface{}) []byte {
	data := logData{}
	logsData := make([][]interface{}, 0)
	for i := range logs {
		logsData = append(logsData, logs[i])
	}
	data.Streams = []struct {
		logDataStream `json:"stream"`
		Values        [][]interface{} `json:"values"`
	}{
		{
			logDataStream: logDataStream{
				Job:         "device",
				DeviceCode:  config.Config().DeviceCode,
				ServiceName: "wuzhen-gateway",
				Env:         "",
			},
			Values: logsData,
		},
	}
	marshal, _ := json.Marshal(data)
	return marshal
}
