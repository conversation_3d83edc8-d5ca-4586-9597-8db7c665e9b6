package status

import (
	"encoding/binary"
	"fmt"
	"gateway/internal/config"
	"gateway/internal/db"
	"gateway/internal/log"
	"gateway/internal/utils"
	"io"
	"net"
	"net/http"
	_ "net/http/pprof"
	"os"
	"os/exec"
	"time"

	"github.com/metacubex/mihomo/hub/executor"

	"github.com/alecthomas/kingpin/v2"
	"github.com/prometheus/client_golang/prometheus"
	promcollectors "github.com/prometheus/client_golang/prometheus/collectors"
	versioncollector "github.com/prometheus/client_golang/prometheus/collectors/version"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"github.com/prometheus/node_exporter/collector"
	"github.com/sagernet/smux"
)

// 定义自定义指标
var (
	xBaseInfo = prometheus.NewGauge(
		prometheus.GaugeOpts{
			Name: "x_base_info",
			ConstLabels: map[string]string{
				"version":   config.Version,
				"timestamp": config.BuildTime,
			},
		},
	)
	xWanDelay = prometheus.NewGauge(
		prometheus.GaugeOpts{
			Name: "x_wan_delay",
		},
	)
	xWanDns = prometheus.NewGauge(
		prometheus.GaugeOpts{
			Name: "x_wan_dns",
			ConstLabels: map[string]string{
				"dns":    "223.5.5.5",
				"domain": "www.baidu.com",
			},
		},
	)
	xWanDelayCountSuccess = prometheus.NewCounter(
		prometheus.CounterOpts{
			Name: "x_wan_delay_count_success",
		},
	)
	xWanDelayCountFail = prometheus.NewCounter(
		prometheus.CounterOpts{
			Name: "x_wan_delay_count_fail",
		},
	)
	xPacketDrop = prometheus.NewCounter(
		prometheus.CounterOpts{
			Name: "x_packet_drop",
		},
	)
	// 客户端使用链路DNS延迟
	xClientDNSDelay = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: "x_client_dns_delay",
		},
		[]string{"path", "circuit"},
	)
	// 客户端使用链路HTTP延迟
	xClientHTTPDelay = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: "x_client_http_delay",
		},
		[]string{"path", "circuit"},
	)
	// 客户端使用链路长连接延迟
	xClientLongDelay = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: "x_client_long_delay",
		},
		[]string{"path", "circuit"},
	)
	// 每个源地址创建的连接数
	xConnectCount = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: "x_connect_count",
		},
		[]string{"src_ip"},
	)
	// 客户端被限制的连接数
	xClientLimitCount = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: "x_client_limit_count",
		},
		[]string{"src_ip"},
	)
	Collectors = []prometheus.Collector{
		xBaseInfo, xWanDelay, xWanDelayCountSuccess, xWanDelayCountFail, xWanDns, xPacketDrop,
		xClientDNSDelay, xClientHTTPDelay, xClientLongDelay, xConnectCount, xClientLimitCount,
	}
)

func startExport() {
	if config.Config().Export != "" {
		var server *smux.Session
		conn := func() {
			dial, err := net.DialTimeout("tcp", config.Config().Export, time.Second*5)
			if err != nil {
				log.GetLogger().Errorf("export Error dialing: %v", err)
				return
			}
			_, err = dial.Write([]byte(config.Config().DeviceCode))
			if err != nil {
				log.GetLogger().Errorf("export Error writing: %v", err)
				return
			}
			server, err = smux.Server(dial, &smux.Config{
				Version:           2,
				KeepAliveInterval: 10 * time.Second,
				KeepAliveTimeout:  30 * time.Second,
				MaxFrameSize:      32768,
				MaxReceiveBuffer:  4194304,
				MaxStreamBuffer:   65536,
			})
			if err != nil {
				log.GetLogger().Errorf("export Error creating smux server: %v", err)
				server = nil
				return
			}
		}
		conn()
		go func() {
			for {
				if server == nil {
					// 指数退避重试机制
					backoff := 3 * time.Second // 初始等待时间
					for {
						time.Sleep(backoff)
						log.GetLogger().Infoln("export Attempting to reconnect...")
						conn()
						if server != nil {
							log.GetLogger().Infoln("export Attempting to reconnect success")
							break // 成功连接，跳出重试循环
						}
						backoff *= 2                 // 指数增加等待时间
						if backoff > 3*time.Minute { // 最大等待时间限制
							backoff = 3 * time.Minute
						}
					}
				}
				stream, err2 := server.Accept()
				if err2 != nil {
					log.GetLogger().Errorf("export Error accepting: %v", err2)
					server = nil
				} else {
					log.GetLogger().Infoln("export Accept stream")
					go handleStream(stream)
				}
			}
		}()
		go func() {
			prometheusServer := http.Server{
				Addr:    "127.0.0.1:9100",
				Handler: prometheusHandler(),
			}
			err := prometheusServer.ListenAndServe()
			if err != nil {
				log.GetLogger().Errorf("export Prometheus error: %v", err)
			}
		}()
	}
}
func handleStream(stream io.ReadWriteCloser) {
	defer func() { _ = stream.Close() }()
	bytes := make([]byte, 4)
	n, err3 := stream.Read(bytes)
	if err3 != nil {
		log.GetLogger().Errorf("export Error reading: %v", err3)
		return
	}
	if n != 4 {
		log.GetLogger().Errorf("export Error reading: %v", err3)
		return
	}
	port := binary.BigEndian.Uint32(bytes)
	if port <= 0 {
		log.GetLogger().Errorln("export binary.BigEndian.Uint32(bytes) <= 0 ")
		return
	}
	if port == 8085 {
		// 下发指令
		signal := make([]byte, 1)
		n, err3 = stream.Read(signal)
		if err3 != nil {
			log.GetLogger().Errorf("signal reading error: %v", err3)
			return
		}
		if n != 1 {
			log.GetLogger().Errorf("no signal received")
			return
		}
		switch signal[0] {
		case 1:
			// SignalHUP
			err := utils.KillHup()
			if err != nil {
				log.GetLogger().Errorf("signal kill hup error: %v", err)
				stream.Write([]byte{1})
				return
			}
			stream.Write([]byte{0})
			return
		case 2:
			// reset DB
			db.Close()
			err := os.Remove("/usr/xrouter/xrouter.db")
			if err != nil {
				log.GetLogger().Errorf("remove db file error: %v", err)
				stream.Write([]byte{1})
			}
			executor.Shutdown()
			stream.Write([]byte{0})
			_ = exec.Command("reboot").Run()
			return
		default:
			log.GetLogger().Warnf("unknown signal %d", signal[0])
			stream.Write([]byte{1})
			return
		}
	}
	conn2, err3 := net.Dial("tcp", fmt.Sprintf("127.0.0.1:%d", port))
	if err3 != nil {
		log.GetLogger().Errorf("export Error dialing: %v", err3)
		return
	}
	go func() {
		defer func() { _ = conn2.Close() }()
		_, _ = io.Copy(conn2, stream)
	}()
	_, _ = io.Copy(stream, conn2)
}
func prometheusHandler() http.Handler {
	kingpin.Parse()
	nilLog := NilLog{}
	registry := prometheus.NewRegistry()
	registry.MustRegister(
		promcollectors.NewProcessCollector(promcollectors.ProcessCollectorOpts{}),
		promcollectors.NewGoCollector(),
	)
	nc, err := collector.NewNodeCollector(nilLog, nil...)
	if err != nil {
		log.GetLogger().Errorf("couldn't create collector: %s", err)
		return nil
	}
	r := prometheus.NewRegistry()
	r.MustRegister(versioncollector.NewCollector("node_exporter"))
	if err = r.Register(nc); err != nil {
		log.GetLogger().Errorf("couldn't register node collector: %s", err)
		return nil
	}
	r.MustRegister(Collectors...)
	handler := promhttp.HandlerFor(
		prometheus.Gatherers{registry, r},
		promhttp.HandlerOpts{
			ErrorLog:            nilLog,
			ErrorHandling:       promhttp.ContinueOnError,
			MaxRequestsInFlight: 40,
			Registry:            registry,
		},
	)
	handler = promhttp.InstrumentMetricHandler(
		registry, handler,
	)
	return handler
}

type NilLog struct {
}

func (n NilLog) Println(_ ...interface{}) {
	return
}
func (n NilLog) Log(_ ...interface{}) error {
	return nil
}
