package status

import (
	"encoding/json"
	"fmt"
	"gateway/internal/config"
	"gateway/internal/log"
	"io"
	"net/http"
	"strconv"
	"strings"
	"time"
)

type getDeviceDetail struct {
	TopupId              string `json:"topupId"`
	ChoiceDeviceId       string `json:"choiceDeviceId"`
	PlanName             string `json:"planName"`
	ChoiceSku            string `json:"choiceSku"`
	ChoiceDataPlanStatus string `json:"choiceDataPlanStatus"`
	CreateTime           string `json:"createTime"`
	ActiveTime           string `json:"activeTime"`
	ExpireTime           string `json:"expireTime"`
	Type                 string `json:"type"`
	CardDataplanId       string `json:"cardDataplanId"`
	DeviceId             string `json:"deviceId"`
	Status               string `json:"status"`
	TerminateTime        string `json:"terminateTime"`
	DataUsage            string `json:"dataUsage"`
	CurrentStatus        string `json:"currentStatus"`
	Eid                  string `json:"eid"`
	UsageToday           string `json:"usageToday"`
	ChoicepApn           string `json:"choicepApn"`
	ChoiceOrderNumber    string `json:"choiceOrderNumber"`
	IsDaily              string `json:"isDaily"`
	DailyResetTime       string `json:"dailyResetTime"`
	EventDetails         []struct {
		EventDate          string `json:"event_date"`
		Eid                string `json:"eid"`
		NotificationStatus string `json:"notification_status"`
		NotifyType         string `json:"notify_type"`
	} `json:"eventDetails"`
	Provider string `json:"provider"`
}

func startEsimTraffic() {
	for {
		time.Sleep(time.Minute * 1)
		if !config.HasFlag(config.FlagEsimDisable) {
			val := esimICCid.Load()
			if val != nil {
				resp, err := http.Get(
					fmt.Sprintf(
						"https://microesim.top/dataPlan/api/getDeviceDetail?deviceId=%s",
						val,
					),
				)
				if err != nil {
					log.GetLogger().Errorf("get esim traffic fail: %v", err)
					continue
				}
				data, err := io.ReadAll(resp.Body)
				if err != nil {
					log.GetLogger().Errorf("get esim traffic fail: %v", err)
					continue
				}
				_ = resp.Body.Close()
				detail := getDeviceDetail{}
				err = json.Unmarshal(data, &detail)
				if err != nil {
					log.GetLogger().Errorf("get esim traffic fail: %v data: %s", err, string(data))
					continue
				}
				switch {
				case strings.Contains(detail.PlanName, "Daily 500MB"):
					totaleSIMTraffic.Store(500 * 1024 * 1024)
				case strings.Contains(detail.PlanName, "Daily 1GB") || strings.Contains(detail.PlanName, "Total 1GB"):
					totaleSIMTraffic.Store(1024 * 1024 * 1024)
				case strings.Contains(detail.PlanName, "Daily 3GB") || strings.Contains(detail.PlanName, "Total 3GB"):
					totaleSIMTraffic.Store(3 * 1024 * 1024 * 1024)
				case strings.Contains(detail.PlanName, "Total 5GB"):
					totaleSIMTraffic.Store(5 * 1024 * 1024 * 1024)
				case strings.Contains(detail.PlanName, "Total 10GB"):
					totaleSIMTraffic.Store(10 * 1024 * 1024 * 1024)
				case strings.Contains(detail.PlanName, "Total 20GB"):
					totaleSIMTraffic.Store(20 * 1024 * 1024 * 1024)
				case strings.Contains(detail.PlanName, "Total 30GB"):
					totaleSIMTraffic.Store(30 * 1024 * 1024 * 1024)
				case strings.Contains(detail.PlanName, "Total 60GB"):
					totaleSIMTraffic.Store(60 * 1024 * 1024 * 1024)
				}
				//fmt.Println(detail.PlanName, totaleSIMTraffic.Load())
				if detail.DataUsage != "" {
					split := strings.Split(detail.DataUsage, " ")
					if len(split) > 1 {
						dataUsage, _ := strconv.ParseFloat(split[0], 64)
						switch strings.ToLower(split[1]) {
						case "kb":
							dataUsage = dataUsage * 1024
						case "mb":
							dataUsage = dataUsage * 1024 * 1024
						case "gb":
							dataUsage = dataUsage * 1024 * 1024 * 1024
						}
						useEsimTraffic.Store(uint64(dataUsage))
					}
				}
			}
		}
	}
}
