#!/bin/bash
check_ip() {
    local interface=$1
    # 使用 ip 命令检查接口是否有 IP 地址
    local ip_address=$(ip addr show "$interface" | grep -w 'inet' | awk '{print $2}')
    if [ -n "$ip_address" ]; then
        echo 0
    else
        echo 1
    fi
}
get_default_gateway() {
    # 使用 ip route 命令获取路由信息
    local route_info=$(ip route | grep default | awk '$8 > 99')
    if [ -n "$route_info" ]; then
        # 提取网关和网卡名
        local interface=$(echo "$route_info" | awk '{print $5}' | head -n 1)
        echo $interface
    fi
}
get_gateway_ip() {
    local interface=$1
    # 使用 ip route 命令获取指定接口的网关信息
    local gateway=$(ip route | grep "default via" | grep "$interface" | awk '{print $3}' | head -n 1)
    if [ -n "$gateway" ]; then
        echo $gateway
    fi
}
rm_tmp_route(){
    ip route | grep "default" | grep "metric 99" | awk '{print $3 " " $5}' | while read -r gateway dev; do ip route del default via "$gateway" dev "$dev"; done
}
PING_1=$(check_ip br-wan)
# 根据函数返回值输出结果
if [ $PING_1 -eq 0 ]; then
    ping -4 -I br-wan -c 2 -W 1 *********  > /dev/null
    PING_1=$?
fi
PING_2=-1
PING_3=-1
if [ "$PING_1" == "1" ]; then
   echo "br-wan no network"
   PING_2=$(check_ip wlan0)
   if [ $? -eq 0 ]; then
        ping -4 -I wlan0 -c 2 -W 1 *********  > /dev/null
        PING_2=$?
   fi
   if [ "$PING_2" == "1" ]; then
      echo "wlan0 no network"
      PING_3=$(check_ip wwan0)
      if [ $? -eq 0 ]; then
            ping -4 -I wwan0 -c 2 -W 1 *********  > /dev/null
            PING_3=$?
      fi
      if [ "$PING_3" == "1" ]; then
            echo "wwan0 no network"
      fi
   fi
fi
DEFAULT_GATEWAY=$(get_default_gateway)
echo br-wan:$PING_1 wlan0:$PING_2 wwan0:$PING_3
if [ "$PING_1" == "0" ]; then
    rm_tmp_route
    exit 0
fi
if [ "$PING_2" == "0" ]; then
    if [ "$DEFAULT_GATEWAY" == "br-wan" ]; then
        echo "set wlan0 99"
        GATEWAY_IP=$(get_gateway_ip wlan0)
        if ip route show | grep -q "metric 99" | grep -q "dev wlan0"; then
            echo "存在 metric 99 的路由，并且设备是 wlan0。"
        else
            echo "不存在 metric 99 的路由，或者设备不是 wlan0。"
            rm_tmp_route
            ip route add default via "$GATEWAY_IP" dev wlan0 metric 99
        fi
        exit 0
    else
      rm_tmp_route
    fi
fi
if [ "$PING_3" == "0" ]; then
    if [ "$DEFAULT_GATEWAY" == "br-wan" ] || [ "$DEFAULT_GATEWAY" == "wlan0" ]; then
        echo "set wwan0 99"
        GATEWAY_IP=$(get_gateway_ip wwan0)
        if ip route show | grep -q "metric 99" | grep -q "dev wwan0"; then
            echo "存在 metric 99 的路由，并且设备是 wwan0"
        else
            echo "不存在 metric 99 的路由，或者设备不是 wwan0。"
            rm_tmp_route
            ip route add default via "$GATEWAY_IP" dev wwan0 metric 99
        fi
        exit 0
    else
      rm_tmp_route
    fi
fi
