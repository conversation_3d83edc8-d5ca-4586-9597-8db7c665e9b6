package status

import (
	"encoding/hex"
	"errors"
	"fmt"
	"gateway/internal/circuitBuilder"
	"gateway/internal/db"
	_interface "gateway/internal/interface"
	"gateway/internal/log"
	"gateway/internal/model"
	"gateway/internal/platformRequest"
	"net"
	"slices"
	"sync"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"gorm.io/gorm"
)

// circuit 电路连接
type circuit struct {
	conn    net.Conn
	builder *circuitBuilder.Builder
}

func (c *circuit) authenticate() error {
	t := platformRequest.GetToken()
	if t == "" {
		return errors.New("token cannot be empty")
	}
	decodeToken, err := hex.DecodeString(t)
	if err != nil {
		return fmt.Errorf("token decode failed, error: %v", err)
	}
	// 发送token进行认证
	c.conn.SetDeadline(time.Now().Add(5 * time.Second))
	_, err = c.conn.Write(decodeToken)
	if err != nil {
		return fmt.Errorf("send token failed: %v", err)
	}
	resp := make([]byte, 1)
	_, err = c.conn.Read(resp)
	if err != nil {
		return err
	}
	if resp[0] != 1 {
		return errors.New("authenticate failed")
	}
	return nil
}

// gaugeResult 测量结果
type gaugeResult struct {
	// 路径名称
	path string
	// 电路名称
	circuit string
	// 延迟
	delay float64
}

type gaugeManager struct {
	// 路径与电路连接的映射
	connMap     map[string]*circuit
	connMapLock sync.Mutex

	// 发起TCP进行测量的地址
	address string

	resultChan chan gaugeResult

	// 需要重连的列表
	retryChan chan string
}

func newGaugeManager(address string) *gaugeManager {
	return &gaugeManager{
		connMap:    make(map[string]*circuit),
		address:    address,
		resultChan: make(chan gaugeResult, 50),
		retryChan:  make(chan string, 50),
	}
}

func (g *gaugeManager) run(list []model.IpRule) {
	// IPRules去重, 构建路径集
	pathSet := make(map[string]struct{})
	for _, rule := range list {
		pathSet[rule.Proxy] = struct{}{}
	}
	// 开始重试
	go g.retryConnect()
	// 开始导出指标到 Prometheus
	go g.export()
	for path := range pathSet {
		err := g.connect(path)
		if err != nil {
			log.GetLogger().Warnln(err)
			continue
		}
	}
	// 开始监听链路改变
	go g.watch()
	// 开始发送心跳
	heartbeatTicker := time.NewTicker(10 * time.Second)
	for {
		<-heartbeatTicker.C
		go g.gauge()
	}
}

func (g *gaugeManager) connect(pathName string) error {
	builder, err := getBuilderFormPath(pathName)
	if err != nil {
		g.retryChan <- pathName
		return fmt.Errorf("error getting builder: %v", err)
	}

	d, err := builder.Build()
	if err != nil {
		g.resultError(pathName, builder.String())
		return fmt.Errorf("error build circuit: %v", err)
	}
	conn, err := d.Dial("tcp", g.address)
	if err != nil {
		g.resultError(pathName, builder.String())
		return fmt.Errorf("error connection: %v", err)
	}

	c := &circuit{conn, builder}
	err = c.authenticate()
	if err != nil {
		g.resultError(pathName, builder.String())
		return err
	}

	g.connMapLock.Lock()
	g.connMap[pathName] = c
	g.connMapLock.Unlock()
	return nil
}

func (g *gaugeManager) gauge() {
	g.connMapLock.Lock()
	defer g.connMapLock.Unlock()
	// 在测量运行时间内，锁住 connMap
	var wg sync.WaitGroup
	for path, c := range g.connMap {
		wg.Add(1)
		go func() {
			defer wg.Done()

			err := c.conn.SetWriteDeadline(time.Now().Add(time.Second * 5))
			if err != nil {
				log.GetLogger().Warnf("Error setting write deadline,  %v", err)
				g.resultError(path, c.builder.String())
				delete(g.connMap, path)
				return
			}

			_, err = c.conn.Write([]byte("ping"))
			if err != nil {
				log.GetLogger().Warnf("Error writing,  %v", err)
				g.resultError(path, c.builder.String())
				delete(g.connMap, path)
				return
			}

			delayStart := time.Now()

			err = c.conn.SetReadDeadline(time.Now().Add(time.Second * 5))
			if err != nil {
				log.GetLogger().Warnf("Error setting read deadline,  %v", err)
				g.resultError(path, c.builder.String())
				delete(g.connMap, path)
				return
			}

			resp := make([]byte, 4)
			_, err = c.conn.Read(resp)
			if err != nil {
				log.GetLogger().Warnf("Error reading,  %v", err)
				g.resultError(path, c.builder.String())
				delete(g.connMap, path)
				return
			}

			delay := time.Since(delayStart).Milliseconds()

			g.resultChan <- gaugeResult{path: path, circuit: c.builder.String(), delay: float64(delay)}

		}()
	}
	wg.Wait()
}

// export 导出到Prometheus指标
func (g *gaugeManager) export() {
	for result := range g.resultChan {
		xClientLongDelay.DeletePartialMatch(prometheus.Labels{"path": result.path})
		xClientLongDelay.WithLabelValues(result.path, result.circuit).Set(result.delay)
	}
}

func (g *gaugeManager) retryConnect() {
	for path := range g.retryChan {
		err := g.connect(path)
		if err != nil {
			log.GetLogger().Debug(err)
			continue
		}
	}
}

// resultError 测量失败，传入重试队列，以及传入超时测量结果
func (g *gaugeManager) resultError(path, c string) {
	g.resultChan <- gaugeResult{path: path, circuit: c, delay: DelayTimeOut}
	go func() {
		// 5秒之后重试
		time.Sleep(5 * time.Second)
		g.retryChan <- path
	}()
}

// watch 监听数据库变更，切换链路 TODO 之后可以换成事件驱动？
func (g *gaugeManager) watch() {
	tc := time.NewTicker(time.Second * 10)
	for {
		<-tc.C
		var ipRules []model.IpRule
		err := db.GetDb().Where("wg = ?", false).Find(&ipRules).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			log.GetLogger().Errorln("get ip rule failed", err)
			continue
		}
		for _, ipRule := range ipRules {
			// 获取IPRule关联的路径
			var path model.Path
			err := db.GetDb().Where("name = ?", ipRule.Proxy).First(&path).Error
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				log.GetLogger().Errorln("get path failed", err)
				continue
			}
			isExists := false
			for k, v := range g.connMap {
				if k == ipRule.Proxy {
					// 去除构建器中的机场节点
					proxies := v.builder.Slice()
					excludePassGFW := proxies[1:]
					if !slices.Equal(excludePassGFW, path.Proxies) {
						// 存在变化，断开原连接，更新并重连
						g.connMapLock.Lock()
						c := g.connMap[ipRule.Proxy]
						c.conn.Close()
						// 从映射中删除，防止测量测到无效电路
						delete(g.connMap, ipRule.Proxy)
						g.connMapLock.Unlock()

						// 从 Prometheus 指标中删除
						row := xClientLongDelay.DeletePartialMatch(
							prometheus.Labels{"path": ipRule.Proxy},
						)
						if row == 0 {
							log.GetLogger().
								Warnf("delete xClientLongDelay metrics failed, path %s", ipRule.Proxy)
						}

						g.retryChan <- ipRule.Proxy
					}
					isExists = true
					break
				}
			}
			if !isExists {
				// 全部遍历也不存在，是新增的链路
				g.retryChan <- ipRule.Proxy
			}
		}

		for path, c := range g.connMap {
			isExists := false
			for _, ipRule := range ipRules {
				if path == ipRule.Proxy {
					isExists = true
					break
				}
			}
			if !isExists {
				// 不存在，为已删除链路
				g.connMapLock.Lock()
				c.conn.Close()
				// 从映射中删除，防止测量测到无效电路
				delete(g.connMap, path)
				g.connMapLock.Unlock()

				// 从 Prometheus 指标中删除
				row := xClientLongDelay.DeletePartialMatch(prometheus.Labels{"path": path})
				if row == 0 {
					log.GetLogger().Warnf("delete xClientLongDelay metrics failed, path %s", path)
				}
			}
		}
	}
}

func getBuilderFormPath(pathName string) (*circuitBuilder.Builder, error) {
	var path model.Path
	err := db.GetDb().Where("name = ?", pathName).First(&path).Error
	if err != nil {
		return nil, err
	}
	// 此处要保证顺序
	orderString := "CASE name \n"
	for i, proxyName := range path.Proxies {
		orderString += fmt.Sprintf("WHEN '%s' THEN %d\n", proxyName, i)
	}
	orderString += "ELSE 3\nEND;"
	var proxies []model.Proxies
	err = db.GetDb().Where("name IN ?", path.Proxies).Order(orderString).Find(&proxies).Error
	if err != nil {
		return nil, err
	}
	// 选择最快的机场节点作为第一跳
	passGFWName, _ := _interface.ClashApi.GetAutoPassGFWNow()
	if passGFWName == "" {
		return nil, errors.New("get auto pass gfw failed")
	}
	var passGFWProxy model.Proxies
	err = db.GetDb().Where("name = ?", passGFWName).First(&passGFWProxy).Error
	if err != nil {
		return nil, err
	}

	// 构建多跳代理
	builder := circuitBuilder.NewBuilder()
	err = builder.Append(append([]model.Proxies{passGFWProxy}, proxies...)...)
	if err != nil {
		return nil, err
	}
	return builder, nil
}
