package status

import (
	"gateway/internal/log"
	"time"

	gocpu "github.com/shirou/gopsutil/cpu"
	gomem "github.com/shirou/gopsutil/mem"
)

func startCpu() {
	for {
		// 获取初始CPU占用率
		percent, err := gocpu.Percent(time.Second, false)
		if err != nil {
			log.GetLogger().Errorln("get cpu percent error", err)
			continue
		}
		cpu.Store(uint64(percent[0]))
		// 获取内存信息
		vmStat, err := gomem.VirtualMemory()
		if err != nil {
			log.GetLogger().Errorln("get mem percent error", err)
			continue
		}
		fp := float64(vmStat.Free) / float64(vmStat.Total) * 100
		mem.Store(uint64(100 - fp))
	}
}
