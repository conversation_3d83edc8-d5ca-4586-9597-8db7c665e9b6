package status

import (
	"context"
	"errors"
	"fmt"
	"gateway/internal/config"
	"gateway/internal/db"
	_interface "gateway/internal/interface"
	"gateway/internal/log"
	"gateway/internal/model"
	"gateway/internal/utils"
	"github.com/coreos/go-iptables/iptables"
	"net"
	"net/http"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/florianl/go-nflog/v2"
	"github.com/vishvananda/netlink"
	"gorm.io/gorm"
)

func startEvent() {
	ch := make(chan netlink.LinkUpdate)
	done := make(chan struct{})
	if err := netlink.LinkSubscribe(ch, done); err != nil {
		log.GetLogger().Errorf("Failed to subscribe to link updates: %s\n", err)
	}
	go func() {
		for {
			select {
			case update := <-ch:
				//只监听有线
				if strings.HasPrefix(update.Link.Attrs().Name, "br-wan") &&
					utils.GetGatewayInterface() != "" {
					state := update.Link.Attrs().OperState
					if state != netlink.OperUp {
						resetNetUptime()
						log.GetLogger().
							Errorln(fmt.Sprintf("Interface %s is not up", update.Link.Attrs().Name))
					}
				}
			}
		}
	}()
	go func() {
		urls := []string{
			"http://www.apple.com/library/test/success.html",
			"https://connectivitycheck.platform.hicloud.com/generate_204",
			"https://www.baidu.com",
			"https://*********",
		}
		index := 0
		countErr := 0
		for {
			delay, err := utils.Delay(
				time.Duration(config.Config().TestDelayTimeout)*time.Millisecond,
				urls[index],
			)
			if err != nil {
				countErr++
				index = (index + 1) % len(urls)
				if countErr == len(urls) {
					log.GetLogger().Errorln(fmt.Sprintf("Delay reset error: %s", err.Error()))
					resetNetUptime()
				}
				//else {
				//	log.GetLogger().Errorf("Delay %d error: %s", countErr, err.Error())
				//}
			} else {
				if _status.GetNetUptime() == 0 {
					countErr = 0
					setNetUptimeNow()
					log.GetLogger().Infoln("SetNetUptimeNow")
				}
			}
			if delay == -1 {
				wanDelay.Store(-1)
				xWanDelay.Set(-1)
				xWanDelayCountFail.Inc()
			} else {
				wanDelay.Store(delay.Milliseconds())
				xWanDelay.Set(float64(delay.Milliseconds()))
				xWanDelayCountSuccess.Inc()
			}
			//dns
			_, err = net.LookupHost("www.baidu.com")
			if err != nil {
				wanDns.Store(false)
				xWanDns.Set(0)
			} else {
				wanDns.Store(true)
				xWanDns.Set(1)
			}
			time.Sleep(time.Second * 5)
		}
	}()
	go func() {
		// 创建一个 iptables 处理器
		ipt, err := iptables.New()
		if err != nil {
			log.GetLogger().Errorf("Failed to create iptables handler: %v", err)
		}
		time.Sleep(time.Second * 5)
		if config.Config().DHCP.Enable { //走网卡测
			var counters []string
			for {
				if ipt != nil {
					counters, _ = ipt.ListWithCounters("filter", "FORWARD")
					for _, counter := range counters {
						if strings.HasPrefix(counter, "-P FORWARD DROP") {
							split := strings.Split(counter, " ")
							if len(split) >= 5 {
								var atoi int
								atoi, err = strconv.Atoi(split[4])
								if err == nil {
									val := uint64(atoi) - dropPackage.Load()
									xPacketDrop.Add(float64(val))
									dropPackage.Swap(uint64(atoi))
								}
							}
						}
					}
				}
				time.Sleep(time.Second * 5)
			}
		}
	}()
	go func() {
		// 等待Clash测速完成后再开始采集指标
		for {
			_, ready := _interface.ClashApi.GetAutoPassGFWNow()
			if ready {
				break
			}
			time.Sleep(3 * time.Second)
		}
		// 每30s执行
		t := time.NewTicker(30 * time.Second)
		for {
			// 获取所有客户端正在使用的链路
			var ipRules []model.IpRule
			err := db.GetDb().Where("wg = ?", false).Find(&ipRules).Error
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				log.GetLogger().Errorln("get ip rule failed", err)
				return
			}
			pathSet := make(map[string]struct{})
			for _, ipRule := range ipRules {
				pathSet[ipRule.Proxy] = struct{}{}
			}
			var wg sync.WaitGroup
			dnsResp := make(chan delayResponse, len(pathSet))
			ttfbResp := make(chan delayResponse, len(pathSet))
			for path := range pathSet {
				wg.Add(1)
				go func() {
					defer wg.Done()
					dns, ttfb, err := delayPath(path)
					if err != nil {
						log.GetLogger().Errorln("delay path failed ", err)
						return
					}
					dnsResp <- dns
					ttfbResp <- ttfb
				}()
			}
			wg.Wait()
			close(dnsResp)
			close(ttfbResp)
			xClientDNSDelay.Reset()
			xClientHTTPDelay.Reset()
			for dns := range dnsResp {
				xClientDNSDelay.WithLabelValues(dns.path, dns.circuit).Set(dns.delay)
			}
			for ttfb := range ttfbResp {
				xClientHTTPDelay.WithLabelValues(ttfb.path, ttfb.circuit).Set(ttfb.delay)
			}
			<-t.C
		}
	}()

	go func() {
		if config.Config().Export == "" {
			return
		}
		host, s, err := net.SplitHostPort(config.Config().Export)
		if err != nil {
			return
		}
		port, err := strconv.Atoi(s)
		if err != nil {
			return
		}
		var ipRules []model.IpRule
		err = db.GetDb().Where("wg = ?", false).Find(&ipRules).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			log.GetLogger().Errorln("get ip rule failed", err)
			return
		}
		// 等待Clash测速完成后再开始采集指标
		for {
			_, ready := _interface.ClashApi.GetAutoPassGFWNow()
			if ready {
				break
			}
			time.Sleep(3 * time.Second)
		}
		gm := newGaugeManager(host + ":" + strconv.Itoa(port+1))
		go gm.run(ipRules)
	}()

	go func() {
		tc := time.NewTicker(time.Second * 8)
		for {
			connectCount := _interface.ClashApi.GetConnectCount()
			xConnectCount.Reset()
			for srcIP, count := range connectCount {
				xConnectCount.WithLabelValues(srcIP.String()).Set(float64(count))
			}
			<-tc.C
		}
	}()

	go func() {
		c := &nflog.Config{
			Group:    100,
			Copymode: nflog.CopyPacket,
			Logger:   log.GetLogger(),
		}
		nf, err := nflog.Open(c)
		if err != nil {
			log.GetLogger().Errorln("open nflog failed", err)
			return
		}
		defer nf.Close()

		ctx, cancel := context.WithCancel(context.Background())
		defer cancel()

		hook := func(attrs nflog.Attribute) int {
			if *attrs.Prefix == "ConnLimit" {
				// 从 Payload 中解析 IPv4Header
				header := utils.ParseIPv4Header(*attrs.Payload)
				if header != nil {
					xClientLimitCount.WithLabelValues(header.SourceIP.String()).Inc()
				}
			}
			return 0
		}

		errFunc := func(e error) int {
			log.GetLogger().Warnf("received error on hook: %v", e)
			return 0
		}

		err = nf.RegisterWithErrorFunc(ctx, hook, errFunc)
		if err != nil {
			log.GetLogger().Errorln("register nflog handler failed", err)
			return
		}

		<-ctx.Done()
	}()

}

type dnsLeakInfo struct {
	ISP         string `json:"ISP"`
	Country     string `json:"Country"`
	City        string `json:"City"`
	IP          string `json:"IP"`
	Leak        bool   `json:"Leak"`
	CountryCode string `json:"CountryCode"`
}

const DelayTimeOut float64 = 10000 // milliseconds

type delayResponse struct {
	path    string
	circuit string
	delay   float64
}

func delayPath(path string) (delayResponse, delayResponse, error) {
	builder, err := getBuilderFormPath(path)
	if err != nil {
		return delayResponse{}, delayResponse{}, err
	}
	d, err := builder.Build()
	if err != nil {
		timeOut := delayResponse{path: path, circuit: builder.String(), delay: DelayTimeOut}
		return timeOut, timeOut, err
	}
	// 使用多跳代理构建HTTP客户端
	client := &http.Client{
		Transport: &http.Transport{
			Dial: d.Dial,
		},
		Timeout: 10 * time.Second,
	}
	defer client.CloseIdleConnections()

	var wg sync.WaitGroup
	dnsRespChan := make(chan float64, 1)
	httpRespChan := make(chan float64, 1)
	wg.Add(2)

	go func() {
		// 默认返回超时
		delay := DelayTimeOut
		defer func(delay *float64) {
			dnsRespChan <- *delay
			wg.Done()
		}(&delay)
		// 检测DNS解析耗时
		// 网关使用http发起DNS解析请求，此处对 https://cloudflare-dns.com/dns-query 发起HTTP请求来模拟DNS请求
		// See: internal/clash/clash.go:338
		dnsReq, err := http.NewRequest("HEAD", "https://cloudflare-dns.com/dns-query", nil)
		if err != nil {
			return
		}

		start := time.Now()
		resp, err := client.Do(dnsReq)
		if err != nil {
			return
		}
		defer resp.Body.Close()
		dns := time.Since(start)
		delay = float64(dns.Milliseconds())
	}()

	go func() {
		// 默认返回超时
		delay := DelayTimeOut
		defer func(delay *float64) {
			httpRespChan <- *delay
			wg.Done()
		}(&delay)
		// 检测请求TTFB，此处会复用缓存排除DNS解析时间的影响
		ttfbReq, err := http.NewRequest("HEAD", "https://cp.cloudflare.com/", nil)
		if err != nil {
			return
		}
		start := time.Now()
		resp, err := client.Do(ttfbReq)
		if err != nil {
			return
		}
		defer resp.Body.Close()
		ttfb := time.Since(start)
		delay = float64(ttfb.Milliseconds())
	}()

	wg.Wait()
	dnsDelay := <-dnsRespChan
	ttfbDelay := <-httpRespChan
	close(dnsRespChan)
	close(httpRespChan)

	return delayResponse{path: path, circuit: builder.String(), delay: dnsDelay},
		delayResponse{path: path, circuit: builder.String(), delay: ttfbDelay},
		nil
}
