package status

import (
	"gateway/internal/config"
	"net/http"
	"time"
)

func startPassGfw() {
	for {
		if config.Config().PassGfw == "auto" {
			// 创建带有超时设置的 http.Client
			client := &http.Client{
				Timeout: 5 * time.Second, // 设置超时时间为 5 秒
			}
			// 发起 GET 请求
			res2, err2 := client.Get("https://google.com")
			if err2 != nil {
				//log.Errorln("check passGfw get google error", err2)
				passGfw.Swap(true)
			} else {
				passGfw.Swap(false)
				_ = res2.Body.Close() // 确保在使用后关闭响应体
			}
		}
		time.Sleep(60 * time.Second)
	}
}
