package status

import (
	"gateway/internal/db"
	"gateway/internal/model"
	"gateway/internal/utils"
	"time"

	"github.com/metacubex/mihomo/tunnel/statistic"
)

func startTraffic() {
	getDb := db.GetDb()
	t := statistic.DefaultManager
	upTotal, downTotal := int64(0), int64(0)
	for {
		snapshot := t.Snapshot()
		up := snapshot.UploadTotal - upTotal
		down := snapshot.DownloadTotal - downTotal
		upTotal = snapshot.UploadTotal
		downTotal = snapshot.DownloadTotal
		_ = model.AddTodayTraffic(getDb, up, down)
		gateway, _ := utils.GetDefaultGateway()
		if gateway == "wwan0" {
			_ = useEsimTraffic.Add(uint64(down) + uint64(up))
			//fmt.Println(fmt.Sprintf("traffic %d  Use %d", totaleSIMTraffic.Load(), traffic))
		}
		time.Sleep(time.Second * 5)
	}
}
