package status

import (
	"gateway/internal/db"
	_interface "gateway/internal/interface"
	"gateway/internal/model"
	"sync"
	"sync/atomic"
	"time"
)

var _status status

type status struct {
}

var netUptime atomic.Uint64
var cpu atomic.Uint64
var mem atomic.Uint64
var activeIpMap = sync.Map{}
var load atomic.Bool
var passGfw atomic.Bool
var wanDelay = atomic.Int64{}
var wanDns = atomic.Bool{}
var tunDelay atomic.Int64
var tunDnsLeak = atomic.Bool{}
var dropPackage atomic.Uint64
var wgIPRuleTableID atomic.Uint64
var totaleSIMTraffic atomic.Uint64
var useEsimTraffic atomic.Uint64
var esimICCid atomic.Value

func init() {
	_status = status{}
	_interface.StatusApi = &_status
}

func (s *status) GetNetUptime() time.Duration {
	if netUptime.Load() == 0 {
		return 0
	}
	return time.Duration(time.Now().Unix()-int64(netUptime.Load())) * time.Second
}
func (s *status) GetCpu() uint64 {
	return cpu.Load()
}
func (s *status) GetMem() uint64 {
	return mem.Load()
}
func (s *status) GetActiveIpMap() *sync.Map {
	return &activeIpMap
}
func (s *status) GetLoad() bool {
	return load.Load()
}
func (s *status) SetLoad(b bool) {
	load.Swap(b)
}
func (s *status) GetPassGfw() bool {
	return passGfw.Load()
}
func (s *status) GetWanDelay() int64 {
	return wanDelay.Load()
}
func (s *status) GetWanDns() bool {
	return wanDns.Load()
}
func (s *status) GetWgIPRuleTableID() uint64 {
	return wgIPRuleTableID.Load()
}
func (s *status) SetWgIPRuleTableID(id uint64) {
	wgIPRuleTableID.Store(id)
}
func (s *status) GetTunDelay() int64 {
	return tunDelay.Load()
}
func (s *status) SetEsimICCid(id string) {
	esimICCid.Store(id)
}
func (s *status) GetEsimICCid() string {
	val := esimICCid.Load()
	if val == nil {
		return ""
	}
	return val.(string)
}
func (s *status) RestUseEsimTraffic() {
	useEsimTraffic.Store(0)
}
func (s *status) GetTotaleSIMTraffic() uint64 {
	return totaleSIMTraffic.Load()
}
func (s *status) GetUseEsimTraffic() uint64 {
	return useEsimTraffic.Load()
}

func resetNetUptime() {
	netUptime.Store(0)
	db.GetDb().Model(model.Proxies{}).Update("delay", -1)
}
func setNetUptimeNow() {
	netUptime.Store(uint64(time.Now().Unix()))
}
