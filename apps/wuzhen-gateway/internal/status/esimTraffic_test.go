package status

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"
	"testing"
)

func TestGetTotalESIMTraffic(t *testing.T) {
	resp, err := http.Get(
		fmt.Sprintf(
			"https://microesim.top/dataPlan/api/getDeviceDetail?deviceId=%s",
			"9000124102382734",
		),
	)
	if err != nil {
		t.Fatalf("get esim traffic fail: %v", err)
	}
	data, err := io.ReadAll(resp.Body)
	if err != nil {
		t.Fatalf("get esim traffic fail: %v", err)
	}
	_ = resp.Body.Close()
	detail := getDeviceDetail{}
	err = json.Unmarshal(data, &detail)
	if err != nil {
		t.Fatalf("get esim traffic fail: %v data: %s", err, string(data))
	}
	switch {
	case strings.Contains(detail.PlanName, "Daily 500MB"):
		totaleSIMTraffic.Store(500 * 1024 * 1024)
	case strings.Contains(detail.PlanName, "Daily 1GB") || strings.Contains(detail.PlanName, "Total 1GB"):
		totaleSIMTraffic.Store(1024 * 1024 * 1024)
	case strings.Contains(detail.PlanName, "Daily 3GB") || strings.Contains(detail.PlanName, "Total 3GB"):
		totaleSIMTraffic.Store(3 * 1024 * 1024 * 1024)
	case strings.Contains(detail.PlanName, "Total 5GB"):
		totaleSIMTraffic.Store(5 * 1024 * 1024 * 1024)
	case strings.Contains(detail.PlanName, "Total 10GB"):
		totaleSIMTraffic.Store(10 * 1024 * 1024 * 1024)
	case strings.Contains(detail.PlanName, "Total 20GB"):
		totaleSIMTraffic.Store(20 * 1024 * 1024 * 1024)
	case strings.Contains(detail.PlanName, "Total 30GB"):
		totaleSIMTraffic.Store(30 * 1024 * 1024 * 1024)
	case strings.Contains(detail.PlanName, "Total 60GB"):
		totaleSIMTraffic.Store(60 * 1024 * 1024 * 1024)
	case strings.Contains(detail.PlanName, "Unlimited"):
		totaleSIMTraffic.Store(9999 * 1024 * 1024 * 1024)
	}
	if detail.DataUsage != "" {
		split := strings.Split(detail.DataUsage, " ")
		if len(split) > 1 {
			dataUsage, _ := strconv.ParseFloat(split[0], 64)
			switch strings.ToLower(split[1]) {
			case "kb":
				dataUsage = dataUsage * 1024
			case "mb":
				dataUsage = dataUsage * 1024 * 1024
			case "gb":
				dataUsage = dataUsage * 1024 * 1024 * 1024
			}
			useEsimTraffic.Store(uint64(dataUsage))
		}
	}
	if totaleSIMTraffic.Load() != 1024*1024*1024 {
		t.Fatalf("get esim traffic fail: %v data: %s", err, string(data))
	}
}
