package status

import (
	"gateway/internal/log"
	"gateway/internal/utils"
	"os"
	"strings"
	"time"
)

func startSms() {
	_, err := os.Stat("/dev/ttyUSB2")
	if err != nil {
		return
	}
	go func() {
		for {
			func() {
				// 配置串口参数
				at, err2 := utils.OpenSerial("/dev/ttyUSB2", 115200)
				if err2 != nil {
					log.GetLogger().Errorln("startSms OpenSerial error", err2)
					return
				}
				defer func() { _ = at.Close() }()
				_, _ = at.SendATCommand(`AT+CSCS="UCS2"`)                  // 设置字符编码
				_, _ = at.SendATCommand(`AT+CMGF=1`)                       // 设置为文本模式
				response, err3 := at.SendATCommand(`AT+CMGL="REC UNREAD"`) // 查询未读短信
				if err3 != nil {
					log.GetLogger().<PERSON><PERSON>rln("startSms readResponse error", err3)
					return
				}
				split := strings.Split(response, "\r\n")
				if len(split) == 12 {
					log.GetLogger().Infoln("Receive sms:", at.ToStr(split[8]))
				}
			}()
			time.Sleep(15 * time.Second)
		}
	}()
}
