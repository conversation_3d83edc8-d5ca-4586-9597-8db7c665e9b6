package log

import (
	"fmt"
	"gateway/internal/config"
	"os"
	"runtime"
	"time"

	mlog "github.com/metacubex/mihomo/log"
	"github.com/sirupsen/logrus"
)

type MyLog struct {
	*logrus.Logger
}

var logger MyLog

// GetLogger 获取日志实例,
// 因为logrus不支持设置caller层级，所以打印日志需要手动调用GetLogger
// like: log.GetLogger().Debugln("test")
func GetLogger() *MyLog {
	return &logger
}

func Init(logLevel string) {
	logger = MyLog{logrus.New()}
	logger.SetOutput(os.Stdout)
	logger.SetFormatter(&logrus.TextFormatter{
		ForceColors:     true,
		FullTimestamp:   true,
		TimestampFormat: time.DateTime + ".000",
		PadLevelText:    true,
		CallerPrettyfier: func(frame *runtime.Frame) (function string, file string) {
			return fmt.Sprintf("%s:%d", frame.Function, frame.Line), ""
		},
	})
	level, err := logrus.ParseLevel(logLevel)
	if err != nil {
		level = logrus.InfoLevel
	}
	logger.SetLevel(level)
	logger.SetReportCaller(true)
	logger.AddHook(&mihomoHandlerHook{})
	logger.AddHook(exporter)

	// 屏蔽clash日志打印，并订阅日志数据推送，接管clash日志
	go func() {
		mlog.SetLevel(mlog.SILENT)
		for event := range mlog.Subscribe() {
			var (
				clashLevel mlog.LogLevel
				exists     bool
			)
			if clashLevel, exists = mlog.LogLevelMapping[config.Config().ClashLogLevel]; !exists {
				clashLevel = mlog.ERROR
			}
			if event.LogLevel < clashLevel {
				continue
			}
			switch event.LogLevel {
			case mlog.DEBUG:
				logger.WithField("caller", event.Caller).Debugln(event.Payload)
			case mlog.INFO:
				logger.WithField("caller", event.Caller).Infoln(event.Payload)
			case mlog.WARNING:
				logger.WithField("caller", event.Caller).Warnln(event.Payload)
			case mlog.ERROR:
				logger.WithField("caller", event.Caller).Errorln(event.Payload)
			default:
				break
			}
		}
	}()
}

func SetLogLevel(logLevel string) {
	level, err := logrus.ParseLevel(logLevel)
	if err != nil {
		level = logrus.InfoLevel
	}
	if logger.Level != level {
		logger.SetLevel(level)
	}
}

type mihomoHandlerHook struct{}

func (hook *mihomoHandlerHook) Levels() []logrus.Level {
	return logrus.AllLevels
}

func (hook *mihomoHandlerHook) Fire(entry *logrus.Entry) error {
	if caller, exists := entry.Data["caller"]; exists {
		if callerString, ok := caller.(string); ok && callerString != "" {
			entry.Caller.Function = callerString
		}
		delete(entry.Data, "caller")
	}
	return nil
}
