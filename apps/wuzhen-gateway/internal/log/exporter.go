package log

import (
	"sync"

	"github.com/sirupsen/logrus"
)

var exporter = &logExporter{
	logChan: make(map[string]chan *logrus.Entry),
}

func Subscribe(topic string) <-chan *logrus.Entry {
	return exporter.subscribe(topic)
}

type logExporter struct {
	logChan map[string]chan *logrus.Entry
	lock    sync.RWMutex
}

func (hook *logExporter) Levels() []logrus.Level {
	// 只导出列出的日志等级
	return []logrus.Level{
		logrus.DebugLevel,
		logrus.InfoLevel,
		logrus.WarnLevel,
		logrus.ErrorLevel,
		logrus.FatalLevel,
		logrus.PanicLevel,
	}
}

func (hook *logExporter) Fire(entry *logrus.Entry) error {
	go func() {
		chanList := make([]chan *logrus.Entry, 0)
		hook.lock.RLock()
		for _, ch := range hook.logChan {
			chanList = append(chanList, ch)
		}
		hook.lock.RUnlock()
		for _, ch := range chanList {
			ch <- entry
		}
	}()
	return nil
}

func (hook *logExporter) subscribe(topic string) <-chan *logrus.Entry {
	hook.lock.RLock()
	if ch, ok := hook.logChan[topic]; ok {
		hook.lock.RUnlock()
		return ch
	}
	hook.lock.RUnlock()
	hook.lock.Lock()
	defer hook.lock.Unlock()
	ch := make(chan *logrus.Entry, 100)
	hook.logChan[topic] = ch
	return ch
}

// TODO 停止订阅
