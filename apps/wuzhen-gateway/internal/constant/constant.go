package constant

var ApTemplate = `
driver=nl80211
auth_algs=1
wpa=2
wpa_pairwise=CCMP
ssid=%s
wpa_passphrase=%s
bridge=br-lan
wpa_key_mgmt=WPA-PSK WPA-PSK-SHA256 SAE
country_code=CN
ieee80211d=1
ieee80211h=1
ieee80211n=1
ieee80211ac=1
ieee80211ax=1
ieee80211w=1
sae_require_mfp=1
hw_mode=a
channel=149
ht_capab=[GF][HT40+][HT40-][LDPC][MAX-AMSDU-7935][RX-STBC1][RXLDPC][SHORT-GI-20][SHORT-GI-40][TX-STBC]
vht_oper_chwidth=1
vht_oper_centr_freq_seg0_idx=155
vht_capab=[MAX-A-MPDU-LEN-EXP3][MAX-MPDU-7991][MU-BEAMFORMEE][MU-BEAMFORMER][RX-ANTENNA-PATTERN][RX-STBC-1][RXLDPC][SHORT-GI-80][SU-BEAMFORMEE][SU-BEAMFORMER][TX-ANTENNA-PATTERN][TX-STBC-2BY1]
he_su_beamformee=1
he_su_beamformer=1
he_mu_beamformer=1
he_basic_mcs_nss_set=2
he_oper_chwidth=1
he_oper_centr_freq_seg0_idx=155
interface=wlan1
disassoc_low_ack=0
preamble=1
wmm_enabled=1
ignore_broadcast_ssid=0
uapsd_advertisement_enabled=1
`
var StaTemplate = `
ctrl_interface=/var/run/wpa_supplicant
update_config=1

network={
    ssid="%s"
    psk="%s"
    key_mgmt=WPA-PSK
}

network={
    ssid="%s"
    psk="%s"
    key_mgmt=WPA-PSK SAE
    ieee80211w=2
}
`
