package dhcp

import (
	"fmt"
	"gateway/internal/config"
	"gateway/internal/db"
	"gateway/internal/log"
	"gateway/internal/model"
	"gateway/internal/utils"
	"net"
	"sync"
	"time"

	"github.com/3th1nk/cidr"
	"github.com/insomniacslk/dhcp/dhcpv4"
	"github.com/insomniacslk/dhcp/dhcpv4/server4"
)

var _cidr *cidr.CIDR
var begin, end, router net.IP
var server *server4.Server
var dnsOps dhcpv4.Option
var lock = &sync.Mutex{}

func handler(conn net.PacketConn, peer net.Addr, req *dhcpv4.DHCPv4) {
	lock.Lock()
	defer lock.Unlock()
	getDb := db.GetDb()
	res, err := dhcpv4.NewReplyFromRequest(req)
	if err != nil {
		log.GetLogger().Errorf("DHCP: failed to build reply: %v", err)
		return
	}
	switch mt := req.MessageType(); mt {
	case dhcpv4.MessageTypeDiscover:
		res.UpdateOption(dhcpv4.OptMessageType(dhcpv4.MessageTypeOffer))
	case dhcpv4.MessageTypeRequest:
		res.UpdateOption(dhcpv4.OptMessageType(dhcpv4.MessageTypeAck))
	case dhcpv4.MessageTypeRelease:
		record := model.GetClientByMac(getDb, req.ClientHWAddr.String())
		if record.Mac != "" {
			err = model.ReleaseClient(getDb, req.ClientHWAddr.String())
			if err != nil {
				log.GetLogger().Errorf("DHCP: failed to free IP: %v", err)
			} else {
				res.UpdateOption(dhcpv4.OptMessageType(dhcpv4.MessageTypeAck))
			}
		}
	case dhcpv4.MessageTypeDecline:
		_ = model.ReleaseClient(getDb, req.ClientHWAddr.String())
		res.UpdateOption(dhcpv4.OptMessageType(dhcpv4.MessageTypeAck))
		return
	case dhcpv4.MessageTypeInform:

	default:
		log.GetLogger().Warnf("DHCP: Unhandled message type: %v", mt)
		return
	}
	//autoconfigure
	{
		if res.MessageType() == dhcpv4.MessageTypeOffer && res.YourIPAddr.IsUnspecified() {
			if _, ok := req.AutoConfigure(); ok {
				res.UpdateOption(dhcpv4.OptAutoConfigure(dhcpv4.DoNotAutoConfigure))
			}
		}
	}
	//DNS
	{
		if req.IsOptionRequested(dhcpv4.OptionDomainNameServer) {
			res.Options.Update(dnsOps)
		}
	}
	//IP router Mask
	{
		mac := req.ClientHWAddr.String()
		record := model.GetClientByMac(getDb, mac)
		hostname := req.HostName()
		if record.Mac == "" {
			record.IP, err = model.AllocateClient(
				getDb,
				hostname,
				mac,
				time.Now().Add(time.Second*time.Duration(config.Config().DHCP.Lease)).Unix(),
				begin,
				end,
			)
			if err != nil {
				log.GetLogger().Errorf("DHCP: failed to allocate IP: %v", err)
				return
			}
		} else {
			err = model.RenewalClient(getDb, mac, hostname, time.Now().Add(time.Second*time.Duration(config.Config().DHCP.Lease)).Unix())
			if err != nil {
				log.GetLogger().Errorf("DHCP: failed to renewal IP: %v", err)
				return
			}
		}
		// gateway
		res.Options.Update(dhcpv4.OptRouter(net.ParseIP(config.Gateway)))
		// mask
		res.Options.Update(dhcpv4.OptSubnetMask(_cidr.CIDR().Mask))
		// ip
		res.YourIPAddr = net.ParseIP(record.IP)
		res.Options.Update(
			dhcpv4.OptIPAddressLeaseTime(time.Duration(config.Config().DHCP.Lease) * time.Second),
		)

	}
	//ServerId
	{
		if req.OpCode == dhcpv4.OpcodeBootRequest {
			if req.ServerIPAddr != nil &&
				!req.ServerIPAddr.Equal(net.IPv4zero) &&
				!req.ServerIPAddr.Equal(router.To4()) {
				return
			}
			res.ServerIPAddr = make(net.IP, net.IPv4len)
			copy(res.ServerIPAddr[:], router.To4())
			res.UpdateOption(dhcpv4.OptServerIdentifier(router.To4()))
		}
	}
	//回复
	if _, err = conn.WriteTo(res.ToBytes(), peer); err != nil {
		log.GetLogger().Errorf("DHCP: WriteTo failed: %v", err)
	}

}
func Server() {
	lAddr := net.UDPAddr{
		IP:   net.ParseIP("0.0.0.0"),
		Port: 67,
	}
	var err error
	server, err = server4.NewServer(config.Config().DHCP.IfName, &lAddr, handler)
	if err != nil {
		log.GetLogger().Errorf("DHCP: NewServer failed: %v", err)
		return
	}
	_cidr, err = cidr.Parse(config.CIDR)
	if err != nil {
		log.GetLogger().Errorf("DHCP: Parse failed: %v", err)
		return
	}
	router = net.ParseIP(config.Gateway)
	dnsArray := []net.IP{net.ParseIP(config.Gateway)}
	dnsOps = dhcpv4.OptDNS(dnsArray...)
	log.GetLogger().
		Infoln(fmt.Sprint("network:", _cidr.CIDR(), " router:", router, " dns:", dnsArray, " broadcast:", _cidr.Broadcast(), " mask:", _cidr.Mask()))
	begin = utils.IncrementIP(router)
	end = utils.DecrementIP(_cidr.Broadcast())
	if err != nil {
		log.GetLogger().Errorf("DHCP: NewBitmapAllocator failed: %v", err)
		return
	}
	go func() {
		err = server.Serve()
		if err != nil {
			log.GetLogger().Errorf("DHCP: Serve failed: %v", err)
		}
	}()
}
func ShutDown() error {
	if server != nil {
		return server.Close()
	}
	return nil
}
