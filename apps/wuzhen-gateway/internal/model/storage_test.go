package model

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestStorageModel_Set(t *testing.T) {
	storageModel := NewStorageModel(db)

	err := storageModel.Set("exampleKey", map[string]interface{}{
		"hello": "world",
	})
	assert.Nil(t, err, "Expected no error when storing data")

	// 断言数据在数据库中存在, 并且值正确
	var s Storage
	err = db.First(&s, "key = ?", "exampleKey").Error
	assert.Nil(t, err, "Expected to find record in database")
	assert.Equal(t, "exampleKey", s.Key, "Stored key should match")

	var storedData map[string]interface{}
	err = json.Unmarshal([]byte(s.Value), &storedData)
	assert.Nil(t, err, "JSON data should be valid")
	assert.Equal(t, "world", storedData["hello"], "Stored value should match")
}

// TestStorageModel_Set_Update 测试键已存在的情况
func TestStorageModel_Set_Update(t *testing.T) {
	storageModel := NewStorageModel(db)

	// Set initial value
	err := storageModel.Set("updateKey", "initialValue")
	assert.Nil(t, err, "Expected no error when setting initial value")

	// Update value
	err = storageModel.Set("updateKey", "updatedValue")
	assert.Nil(t, err, "Expected no error when updating value")

	// Check if updated successfully
	result, err := storageModel.Get("updateKey")
	assert.Nil(t, err, "Expected no error when getting updated value")
	assert.Equal(t, "updatedValue", result, "Expected value to be updated")
}

func TestStorageModel_Get(t *testing.T) {
	storageModel := NewStorageModel(db)

	err := storageModel.Set("exampleKey", map[string]interface{}{
		"foo": "bar",
	})
	assert.Nil(t, err, "Expected no error when storing data")

	result, err := storageModel.Get("exampleKey")
	assert.Nil(t, err, "Expected no error when getting data")
	assert.NotNil(t, result, "Expected to find result")

	resultMap, ok := result.(map[string]interface{})
	assert.True(t, ok, "Expected result to be a map")
	assert.Equal(t, "bar", resultMap["foo"], "Expected value to match")
}

// TestStorageModel_Get_NotFound 测试键不存在的情况
func TestStorageModel_Get_NotFound(t *testing.T) {
	storageModel := NewStorageModel(db)

	result, err := storageModel.Get("nonExistentKey")
	assert.Nil(t, err, "Expected no error when getting a non-existent key")
	assert.Equal(t, "", result, "Expected empty result for non-existent key")
}

func TestStorageModel_Del(t *testing.T) {
	storageModel := NewStorageModel(db)

	err := storageModel.Set("deleteKey", map[string]interface{}{
		"toBeDeleted": true,
	})
	assert.Nil(t, err, "Expected no error when storing data")

	err = storageModel.Del("deleteKey")
	assert.Nil(t, err, "Expected no error when deleting data")

	result, err := storageModel.Get("deleteKey")
	assert.Nil(t, err, "Expected no error when getting deleted key")
	assert.Equal(t, "", result, "Expected no value after deletion")
}
