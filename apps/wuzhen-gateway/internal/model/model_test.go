package model

import (
	"log"
	"os"
	"testing"
	"time"

	"github.com/glebarez/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

var db *gorm.DB

func TestMain(m *testing.M) {
	newLogger := logger.New(
		log.New(os.Stdout, "\r\n", log.LstdFlags),
		logger.Config{
			SlowThreshold:             time.Second,
			LogLevel:                  logger.Silent,
			IgnoreRecordNotFoundError: true,
			Colorful:                  false,
		},
	)
	// 使用 SQLite 内存模式数据库进行测试，每次测试用完就会消失，不影响实际数据
	var err error
	db, err = gorm.Open(sqlite.Open(":memory:"), &gorm.Config{
		Logger: newLogger,
	})
	if err != nil {
		panic(err)
	}

	// 自动创建表，模拟生产环境中的数据表
	err = db.AutoMigrate(
		&Proxies{},
		&IpRule{},
		&Path{},
		&User{},
		&LoginLog{},
		Traffic{},
		Client{},
		Config{},
		Storage{},
	)

	if err != nil {
		panic(err)
	}

	m.Run()
}
