package model

import (
	"errors"
	"fmt"
	"gateway/internal/config"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type Path struct {
	Name               string     `gorm:"primary_key;comment:名称"`
	Proxies            []string   `gorm:"serializer:json;comment:路径列表"`
	Exclusive          string     `gorm:"comment:独占"`
	UserID             uuid.UUID  `gorm:"comment:用户id"`
	ChangeTime         uint64     `gorm:"comment:变更时间"                 json:"change_time"` //0 则不开启。单位秒
	ChangeCountryArray [][]string `gorm:"serializer:json;comment:变更国家" json:"change_country_array"`
	Base
}

const (
	PathExclusiveNone = "none"
	PathExclusiveOut  = "out"
	PathExclusiveALL  = "all"
)

func AddOrUpdatePath(db *gorm.DB, p *Path, newName string, check bool) (err error) {
	if p.Name == "" {
		return errors.New("name is empty")
	}
	db = db.Begin()
	defer func() {
		if err != nil {
			db.Rollback()
		} else {
			db.Commit()
		}
	}()
	old := Path{}
	db.Model(&Path{}).Where("name = ?", p.Name).First(&old)
	db.Model(&Path{}).Where("name = ?", p.Name).Delete(&Path{}) //先删除
	if old.Base.CreatedAt > 0 {
		p.Base = old.Base
		p.UserID = old.UserID
		if newName != "" {
			p.Name = newName
			db.Exec(`UPDATE ip_rule SET proxy = ? WHERE proxy = ?`, p.Name, old.Name)
		}
	}
	if p.Exclusive == "" {
		p.Exclusive = PathExclusiveNone
	}
	if p.Exclusive != PathExclusiveNone {
		//判断是否达到独占上限
		count := int64(0)
		db.Model(&Proxies{}).
			Where("type !=? AND use_status = ?", ProxyTypePassGfw, ProxyUseStatusExclusive).
			Count(&count)
		if uint(count) >= config.Config().ExclusiveNum {
			return errors.New(
				fmt.Sprintf("exclusive exceeds limit:%d", config.Config().ExclusiveNum),
			)
		}
	}
	if check {
		//使用累计计数
		paths := make([]*Path, 0)
		db.Model(&Path{}).Where("name IN (SELECT proxy FROM ip_rule)").Find(&paths)
		useCount := make(map[string]int)
		for _, _p := range paths {
			for i, proxy := range _p.Proxies {
				if p.Exclusive == PathExclusiveALL {
					useCount[proxy] = -1
				} else if _p.Exclusive == PathExclusiveOut && i == len(_p.Proxies)-1 {
					useCount[proxy] = -1
				} else if useCount[proxy] != -1 {
					useCount[proxy]++
				}
			}
		}
		for i, proxy := range p.Proxies {
			//判断是否为可用状态
			if useCount[proxy] == -1 {
				return errors.New("already exclusive") //已经被独占
			}
			if p.Exclusive == PathExclusiveALL ||
				(p.Exclusive == PathExclusiveOut && i == len(p.Proxies)-1) {
				if useCount[proxy] > 0 {
					return errors.New("already use") //已经被使用
				}
			}
		}
	}
	err = db.Save(&p).Error
	return err
}
func CheckPathUse(db *gorm.DB) {
	paths := make([]*Path, 0)
	rules := make([]*IpRule, 0)
	db.Model(&Path{}).Select("name", "exclusive", "change_time").Find(&paths)
	db.Model(&IpRule{}).Find(&rules)
	rulesMap := make(map[string]struct{})
	for _, rule := range rules {
		rulesMap[rule.Proxy] = struct{}{}
	}
	for _, path := range paths { //判断是否有人直接使用这个节点
		if _, has := rulesMap[path.Name]; !has {
			if path.Exclusive != PathExclusiveNone {
				db.Model(&Path{}).
					Where("name = ?", path.Name).
					Update("exclusive", PathExclusiveNone)
			}
			if path.ChangeTime > 0 {
				db.Model(&Path{}).Where("name = ?", path.Name).Update("change_time", 0)
			}
		}
	}
}
func GetPath(db *gorm.DB, name string) Path {
	p := Path{}
	db.Model(&Path{}).Where("name = ?", name).First(&p)
	return p
}
func GetPathsByUser(db *gorm.DB, uid uuid.UUID) []*Path {
	paths := make([]*Path, 0)
	db.Model(&Path{}).Where("user_id = ?", uid).Find(&paths)
	return paths
}
func GetPathUseCount(db *gorm.DB, name string) int64 {
	count := int64(0)
	db.Model(&IpRule{}).Where("proxy = ?", name).Count(&count)
	return count
}
