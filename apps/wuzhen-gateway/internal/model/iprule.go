package model

import (
	"gorm.io/gorm"
)

type IpRule struct {
	IP    string `gorm:"primary_key;comment:ip地址"`
	Proxy string `gorm:"comment:链路名"`
	Wg    bool   `gorm:"comment:wg模式"`
	Base
}

func AddOrUpdateIpRule(db *gorm.DB, ip string, path string, Wg bool) error {
	ipRule := IpRule{}
	db.Model(&IpRule{}).Where("ip = ?", ip).First(&ipRule)
	return db.Save(&IpRule{
		IP:    ip,
		Proxy: path,
		Base:  ipRule.Base,
		Wg:    Wg,
	}).Error
}
func GetIpRule(db *gorm.DB, ip string) *IpRule {
	ipRule := IpRule{}
	db.Model(&IpRule{}).Where("ip = ?", ip).First(&ipRule)
	return &ipRule
}
func DeleteIpRule(db *gorm.DB, ip string) error {
	return db.Where("ip = ?", ip).Delete(&IpRule{}).Error
}
