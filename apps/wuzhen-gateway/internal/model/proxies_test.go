package model

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestAddOrUpdateProxies(t *testing.T) {
	// 插入记录
	proxy1 := &Proxies{
		Name:        "Test1",
		Server:      "127.0.0.1",
		Port:        8080,
		Type:        ProxyTypePassGfw,
		Protocol:    "http",
		CountryCode: "US",
		UseStatus:   ProxyUseStatusExclusive,
	}

	err := AddOrUpdateProxies(db, proxy1)
	assert.NoError(t, err, "AddOrUpdateProxies should not return an error")

	// 查询插入后的记录
	var result Proxies
	db.Where("name = ?", proxy1.Name).First(&result)

	assert.Equal(t, proxy1.Name, result.Name, "Inserted name should match")
	assert.Equal(t, "us", result.CountryCode, "CountryCode should be lowercased and match")
	assert.Equal(t, ProxyUseStatusExclusive, result.UseStatus, "UseStatus should match")

	// 更新记录
	proxy1.UseStatus = ProxyUseStatusNone
	err = AddOrUpdateProxies(db, proxy1)
	assert.NoError(t, err, "AddOrUpdateProxies should not return an error on update")

	var result1 Proxies
	db.Where("name = ?", proxy1.Name).First(&result1)
	assert.Equal(t, ProxyUseStatusNone, result1.UseStatus, "Updated UseStatus should match")
}

func TestGetProxy(t *testing.T) {
	// 插入记录
	proxy := &Proxies{Name: "Test2", UseStatus: ProxyUseStatusUse}
	db.Create(proxy)

	// 查询
	result := GetProxy(db, "Test2")
	assert.NotNil(t, result, "Proxy should not be nil")
	assert.Equal(t, "Test2", result.Name, "Proxy name should be 'Test2'")
}

func TestCanUserProxies(t *testing.T) {
	// 插入记录1
	proxy1 := &Proxies{Name: "Test3", UseStatus: ProxyUseStatusUse}
	db.Create(proxy1)

	// 插入记录2
	proxy2 := &Proxies{Name: "Test4", UseStatus: ProxyUseStatusNone}
	db.Create(proxy2)

	// 查询返回true，因为两个proxies的状态都不是 "exclusive"
	canUse := CanUserProxies(db, "Test3", "Test4")
	assert.True(t, canUse, "CanUserProxies should return true")
}
