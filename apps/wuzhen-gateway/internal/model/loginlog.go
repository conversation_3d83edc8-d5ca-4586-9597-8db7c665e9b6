package model

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type LoginState string

func (s LoginState) String() string {
	return string(s)
}

var (
	LoginStateSuccess LoginState = "success"
	LoginStateFail    LoginState = "fail"
)

type LoginLog struct {
	Base
	ID      uuid.UUID  `gorm:"comment:记录ID;primary_key"`
	State   LoginState `gorm:"comment:登录状态"`
	IP      string     `gorm:"comment:登录IP"`
	Account string     `gorm:"comment:登录账号"`
	UserId  *uuid.UUID `gorm:"comment:用户ID"`
}

func NewLoginLog(state LoginState, ip string, account string, userId *uuid.UUID) *LoginLog {
	return &LoginLog{
		ID:      uuid.New(),
		State:   state,
		IP:      ip,
		Account: account,
		UserId:  userId,
	}
}

// GetAccountLoginFailCount GetAccountLoginCount 获取指定账号在指定时间内登录失败次数 如果指定时间内有成功记录则按最近一次成功记录时间开始
func GetAccountLoginFailCount(db *gorm.DB, account string, duration time.Duration) (int64, error) {
	//获取上一次登录成功的时间
	var loginLog LoginLog
	db.Model(&LoginLog{}).
		Where("account = ? AND state = ?", account, LoginStateSuccess.String()).
		Order("created_at desc").
		First(&loginLog)
	if loginLog.ID != uuid.Nil {
		duration = time.Since(time.Unix(loginLog.CreatedAt, 0))
	}
	endTime := time.Now()
	startTime := endTime.Add(-duration)
	var count int64
	err := db.Model(&LoginLog{}).Where(
		"account = ? AND created_at > ? AND created_at <= ? AND state = ?",
		account,
		startTime.Unix(),
		endTime.Unix(),
		string(LoginStateFail),
	).Count(&count).Error
	if err != nil {
		return 0, err
	}
	return count, nil
}

// GetLastLoginLog 获取账号最后一次登录信息
func GetLastLoginLog(db *gorm.DB, id uuid.UUID) (*LoginLog, error) {
	var loginLog LoginLog
	err := db.Model(&LoginLog{}).
		Where("user_id = ? AND state = ?", id.String(), LoginStateSuccess.String()).
		Order("created_at desc").
		First(&loginLog).
		Error
	return &loginLog, err
}
