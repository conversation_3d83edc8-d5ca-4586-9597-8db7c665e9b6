package model

import (
	"time"

	"gorm.io/gorm"
)

type Traffic struct {
	Date string `gorm:"primary_key;comment:日期"`
	Up   int64  `gorm:"comment:上传"`
	Down int64  `gorm:"comment:下载"`
	Base
}

func GetLastXDayTraffic(db *gorm.DB, x int) []*Traffic {
	traffic := make([]*Traffic, 0)
	db = db.Model(&Traffic{}).
		Order("date desc").
		Where("date >= ?", time.Now().AddDate(0, 0, -x)).
		Find(&traffic)
	return traffic
}
func AddTodayTraffic(db *gorm.DB, up, down int64) error {
	traffic := Traffic{
		Date: time.Now().Format("2006-01-02"),
		Up:   up,
		Down: down,
	}
	db.Model(&Traffic{}).Where("date = ?", time.Now().Format("2006-01-02")).FirstOrCreate(&traffic)
	if traffic.Up == up && traffic.Down == down {
		return nil
	}
	return db.Model(&Traffic{}).
		Where("date = ?", time.Now().Format("2006-01-02")).
		Update("up", traffic.Up+up).
		Update("down", traffic.Down+down).
		Error
}
