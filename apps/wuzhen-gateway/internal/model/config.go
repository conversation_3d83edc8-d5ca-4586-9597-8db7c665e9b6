package model

import (
	"errors"

	"gorm.io/gorm"
)

const (
	UpstreamSSID       = "upstream_wifi_ssid"
	UpstreamPassword   = "upstream_wifi_password"
	UpstreamSwitch     = "upstream_wifi_switch"
	DownstreamSSID     = "downstream_wifi_ssid"
	DownstreamPassword = "downstream_wifi_password"
	DownstreamSwitch   = "downstream_wifi_switch"
)

type Config struct {
	Base
	Key   string `gorm:"comment:配置键"`
	Value string `gorm:"comment:配置值"`
}

func GetConfig(db *gorm.DB, key string) (string, bool) {
	var c Config
	err := db.Where("key = ?", key).First(&c).Error
	if err != nil {
		return "", false
	}
	return c.Value, true
}

func SetConfig(db *gorm.DB, key string, value string) error {
	var c Config

	err := db.Where("key = ?", key).First(&c).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}
	if c.Key == key {
		// 存在配置， 更新
		err = db.Model(&c).Where("key = ?", key).Update("value", value).Error
		if err != nil {
			return err
		}
	} else {
		// 不存在, 新增
		c.Key = key
		c.Value = value
		err = db.Create(&c).Error
		if err != nil {
			return err
		}
	}
	return nil
}
func HasConfig(db *gorm.DB, key string) bool {
	var c Config
	err := db.Where("key = ?", key).First(&c).Error
	if err != nil {
		return false
	}
	return true
}
