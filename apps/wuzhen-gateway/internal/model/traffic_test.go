package model

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

// TestGetLastXDayTraffic 测试 GetLastXDayTraffic 方法能否按预期工作。
func TestGetLastXDayTraffic(t *testing.T) {
	// 插入测试数据
	db.Create(&Traffic{Date: time.Now().Format("2006-01-02"), Up: 100, Down: 200})
	db.Create(&Traffic{Date: time.Now().AddDate(0, 0, -1).Format("2006-01-02"), Up: 150, Down: 250})
	db.Create(&Traffic{Date: time.Now().AddDate(0, 0, -2).Format("2006-01-02"), Up: 200, Down: 300})

	// 测试获取前2天的流量数据
	traffic := GetLastXDayTraffic(db, 2)
	assert.Equal(t, 2, len(traffic), "Expected to retrieve 2 days of traffic")
	assert.Equal(t, int64(100), traffic[0].Up, "Expected correct 'up' value for first day")
	assert.Equal(t, int64(200), traffic[0].Down, "Expected correct 'down' value for first day")
	assert.Equal(t, int64(150), traffic[1].Up, "Expected correct 'up' value for second day")
	assert.Equal(t, int64(250), traffic[1].Down, "Expected correct 'down' value for second day")
}

// TestAddTodayTraffic 测试 AddTodayTraffic 能否正确添加或更新当天的流量信息。
func TestAddTodayTraffic(t *testing.T) {
	// 添加当天的流量数据
	err := AddTodayTraffic(db, 100, 200)
	assert.Nil(t, err, "Expected no error when adding today's traffic")

	// 检查当天的流量数据是否添加成功
	var traffic Traffic
	err = db.First(&traffic, "date = ?", time.Now().Format("2006-01-02")).Error
	assert.Nil(t, err, "Expected to find today's traffic in the database")
	assert.Equal(t, int64(100), traffic.Up, "Expected 'up' traffic to be 100")
	assert.Equal(t, int64(200), traffic.Down, "Expected 'down' traffic to be 200")

	// 再次添加同一天的数据（应该更新现存的记录）
	err = AddTodayTraffic(db, 50, 75)
	assert.Nil(t, err, "Expected no error when updating today's traffic")

	// 检查当天的流量数据是否更新正确
	err = db.First(&traffic, "date = ?", time.Now().Format("2006-01-02")).Error
	assert.Nil(t, err, "Expected to find updated today's traffic in the database")
	assert.Equal(t, int64(150), traffic.Up, "Expected 'up' traffic to be updated to 150")
	assert.Equal(t, int64(275), traffic.Down, "Expected 'down' traffic to be updated to 275")
}
