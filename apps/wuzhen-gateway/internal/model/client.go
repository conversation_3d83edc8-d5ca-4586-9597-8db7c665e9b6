package model

import (
	"gateway/internal/utils"
	"net"

	"gorm.io/gorm"
)

type Client struct {
	Mac          string `gorm:"primary_key;comment:mac地址"`
	Hostname     string `gorm:"comment:主机名"`
	IP           string `gorm:"unique,comment:IP地址"`
	LeaseEndTime int64  `gorm:"comment:租约到期时间"`
	Static       bool   `gorm:"comment:内置静态地址"`
	Base
}

func ReleaseClient(db *gorm.DB, mac string) error {
	return db.Delete(&Client{}, "mac = ?", mac).Error
}

func GetClientByIP(db *gorm.DB, ip string) *Client {
	client := Client{}
	db.Where("ip = ?", ip).First(&client)
	return &client
}
func GetClientByMac(db *gorm.DB, mac string) *Client {
	client := Client{}
	db.Where("mac = ?", mac).First(&client)
	return &client
}

func AllocateClient(
	db *gorm.DB,
	hostname, mac string,
	leaseTime int64,
	begin, end net.IP,
) (string, error) {
	var ipRange net.IP
	var client *Client
	for {
		ipRange = utils.RandomIPInRange(begin, end)
		client = GetClientByIP(db, ipRange.String())
		if client.Mac == "" {
			break
		}
	}
	client.Mac = mac
	client.Hostname = hostname
	client.IP = ipRange.String()
	client.LeaseEndTime = leaseTime
	return client.IP, db.Save(client).Error
}
func RenewalClient(db *gorm.DB, mac, hostname string, leaseTime int64) error {
	return db.Model(&Client{}).
		Where("mac = ?", mac).
		Updates(map[string]interface{}{"hostname": hostname, "lease_end_time": leaseTime}).
		Error
}

func GetClientByIp(db *gorm.DB, ip string) *Client {
	var res Client
	db.Model(&Client{}).Where("ip = ? ", ip).First(&res)
	return &res
}

func GetClients(db *gorm.DB) []*Client {
	var list []*Client
	db.Model(&Client{}).Find(&list)
	return list
}
