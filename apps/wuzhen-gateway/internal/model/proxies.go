package model

import (
	"errors"
	"strings"
	"sync"

	"gorm.io/gorm"
)

const (
	ProxyTypePassGfw    = "pass_gfw"
	ProxyTypeGuardProxy = "guard"
	ProxyTypeExitProxy  = "exit"
	ProxyAuto           = "auto"
	ProxyAutoGuard      = "auto_guard"
	ProxyBalance        = "balance"
)
const (
	ProxyUseStatusNone      = "none"
	ProxyUseStatusUse       = "use"
	ProxyUseStatusExclusive = "exclusive"
	ProxyUseStatusDiscard   = "discard"
)

const (
	ProxySourcePlatform = "platform"
	ProxySourceHit      = "hit"
	ProxySourceImported = "imported"
)

var ProxiesLock sync.Mutex

type Proxies struct {
	Name        string         `gorm:"primary_key;comment:节点名"`
	Server      string         `gorm:"comment:节点地址"`
	Port        int64          `gorm:"comment:节点端口"`
	Type        string         `gorm:"comment:节点用途类型"`
	Protocol    string         `gorm:"comment:节点协议"`
	CountryName string         `gorm:"comment:国家地区名"`
	CountryCode string         `gorm:"comment:国家地区编号"`
	CityName    string         `gorm:"comment:城市名"`
	Config      map[string]any `gorm:"comment:配置数据;serializer:json"`
	Delay       int64          `gorm:"comment:延迟"`
	Raw         map[string]any `gorm:"comment:原始数据;serializer:json"`
	UseStatus   string         `gorm:"comment:使用状态"`
	Source      string         `gorm:"comment:节点来源;default:platform"`
	Base
}

func AddOrUpdateProxies(db *gorm.DB, p *Proxies) error {
	// 检查 UseStatus 的有效性，并设置为 ProxyUseStatusNone（无）如果不有效
	switch p.UseStatus {
	case ProxyUseStatusUse, ProxyUseStatusExclusive:
		// 有效状态，保持不变
	default:
		p.UseStatus = ProxyUseStatusNone
	}
	p.CountryCode = strings.ToLower(p.CountryCode)
	// 尝试查找记录，如果不存在则创建
	var proxy Proxies
	err := db.Where(Proxies{Name: p.Name}).First(&proxy).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return db.Create(p).Error
	} else {
		return db.Model(p).Updates(p).Error
	}
}
func CheckProxiesUse(getDb *gorm.DB) {
	paths := make([]*Path, 0)
	getDb.Model(&Path{}).Where("name IN (SELECT proxy FROM ip_rule)").Find(&paths)
	proxies := make([]*Proxies, 0)
	getDb.Model(&Proxies{}).Select("name", "use_status").Find(&proxies)
	for _, p := range proxies {
		if p.UseStatus == ProxyUseStatusDiscard {
			continue
		}
		UseStatus := ProxyUseStatusNone
		for _, path := range paths {
			for i, proxy := range path.Proxies {
				if proxy == p.Name {
					UseStatus = ProxyUseStatusUse
					if path.Exclusive == PathExclusiveALL {
						UseStatus = ProxyUseStatusExclusive
					}
					if path.Exclusive == PathExclusiveOut && i == len(path.Proxies)-1 {
						UseStatus = ProxyUseStatusExclusive
					}
					break
				}
			}
		}
		if UseStatus != p.UseStatus {
			getDb.Model(&Proxies{}).Where("name = ?", p.Name).Update("use_status", UseStatus)
		}
	}

}
func GetProxy(db *gorm.DB, name string) *Proxies {
	p := Proxies{}
	db.Where("name = ?", name).First(&p)
	return &p
}
func CanUserProxies(db *gorm.DB, name ...string) bool {
	if len(name) == 0 {
		return false
	}
	count := int64(0)
	db.Model(&Proxies{}).
		Where("name IN (?) AND use_status != ?", name, ProxyUseStatusExclusive).
		Count(&count)
	if count != int64(len(name)) {
		return false
	}
	return true
}
