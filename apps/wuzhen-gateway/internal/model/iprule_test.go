package model

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

var tests = map[string]struct {
	ip    string
	proxy string
	wg    bool
}{
	"insert_1": {ip: "***********", proxy: "test_proxy", wg: false},
	"insert_2": {ip: "***********", proxy: "test_proxy_1", wg: false},
	"update_1": {ip: "***********", proxy: "test_proxy_update", wg: true},
}

func TestAddOrUpdateIpRule1(t *testing.T) {
	// 是否能够执行
	for _, tt := range tests {
		err := AddOrUpdateIpRule(db, tt.ip, tt.proxy, tt.wg)
		assert.NoError(t, err)
		var vaildIpRule IpRule
		db.Model(&IpRule{}).
			Where("ip = ? AND proxy = ? AND wg = ?", tt.ip, tt.proxy, tt.wg).
			First(&vaildIpRule)
		assert.Equal(t, tt.ip, vaildIpRule.IP)
	}

	// 新增数量是否正确
	ipRuleList := make([]IpRule, 0)
	db.Model(&IpRule{}).Find(&ipRuleList)
	assert.Len(t, ipRuleList, 2)

	// 是否修改成功
	var updateIpRule IpRule
	db.Model(&IpRule{}).Where("ip = ?", tests["insert_1"].ip).First(&updateIpRule)

	type S struct {
		proxy string
		wg    bool
	}
	assert.EqualExportedValues(
		t,
		S{tests["update_1"].proxy, tests["update_1"].wg},
		S{updateIpRule.Proxy, updateIpRule.Wg},
	)

}

func TestDeleteIpRule(t *testing.T) {
	err := DeleteIpRule(db, tests["insert_2"].ip)
	assert.NoError(t, err)
}

func TestGetIpRule(t *testing.T) {
	iprule1 := GetIpRule(db, tests["insert_1"].ip)
	assert.Equal(t, tests["insert_1"].ip, iprule1.IP)
	iprule2 := GetIpRule(db, tests["insert_2"].ip)
	assert.Equal(t, "", iprule2.IP)
}
