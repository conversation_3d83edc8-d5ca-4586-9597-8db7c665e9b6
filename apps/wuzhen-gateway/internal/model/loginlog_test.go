package model

import (
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
)

// 之前没有登录成功记录
func TestGetAccountLoginFailCount_NoPreviousSuccess(t *testing.T) {
	account := "test_account"
	duration := 10 * time.Minute

	// 插入一条失败记录
	db.Create(&LoginLog{
		ID:      uuid.New(),
		State:   LoginStateFail,
		IP:      "127.0.0.1",
		Account: account,
		UserId:  nil,
		Base:    Base{CreatedAt: time.Now().Add(-duration / 2).Unix()},
	})

	count, err := GetAccountLoginFailCount(db, account, duration)
	assert.NoError(t, err)
	assert.Equal(t, int64(1), count)
}

// 之前有登录成功记录
func TestGetAccountLoginFailCount_WithPreviousSuccess(t *testing.T) {
	account := "test_account"
	duration := 10 * time.Minute

	// 插入在指定之前的成功记录
	db.Create(&LoginLog{
		ID:      uuid.New(),
		State:   LoginStateSuccess,
		IP:      "127.0.0.1",
		Account: account,
		UserId:  nil,
		Base:    Base{CreatedAt: time.Now().Add(-15 * time.Minute).Unix()},
	})
	count, err := GetAccountLoginFailCount(db, account, duration)
	assert.NoError(t, err)
	assert.Equal(t, int64(1), count)

	// 插入在指定之后的成功记录
	db.Create(&LoginLog{
		ID:      uuid.New(),
		State:   LoginStateSuccess,
		IP:      "127.0.0.1",
		Account: account,
		UserId:  nil,
		Base:    Base{CreatedAt: time.Now().Add(-duration / 2).Unix()},
	})

	count, err = GetAccountLoginFailCount(db, account, duration)
	assert.NoError(t, err)
	assert.Equal(t, int64(0), count)
}

func TestGetLastLoginLog(t *testing.T) {
	userId := uuid.New()

	// 插入一条登录成功记录
	successfulLog := LoginLog{
		ID:      uuid.New(),
		State:   LoginStateSuccess,
		IP:      "127.0.0.1",
		Account: "test_account",
		UserId:  &userId,
		Base:    Base{CreatedAt: time.Now().Add(-5 * time.Minute).Unix()},
	}
	db.Create(&successfulLog)

	log, err := GetLastLoginLog(db, userId)
	assert.NoError(t, err)
	assert.Equal(t, successfulLog.ID, log.ID)
	assert.Equal(t, LoginStateSuccess.String(), log.State.String())
}
