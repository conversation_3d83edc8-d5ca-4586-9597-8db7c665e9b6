package model

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

var dataMap = map[string]string{
	UpstreamSSID:       "test_upstream_wifi_ssid",
	UpstreamPassword:   "test_upstream_wifi_password",
	DownstreamSSID:     "test_downstream_wifi_ssid",
	DownstreamPassword: "test_downstream_wifi_password",
}

func TestSetConfig(t *testing.T) {
	for key, value := range dataMap {
		err := SetConfig(db, key, value)
		assert.NoError(t, err)
	}
}

func TestGetConfig(t *testing.T) {
	for key, value := range dataMap {
		v, exists := GetConfig(db, key)
		assert.True(t, exists)
		assert.Equal(t, value, v)
	}
	_, exists := GetConfig(db, "unknown_key")
	assert.False(t, exists)
}
