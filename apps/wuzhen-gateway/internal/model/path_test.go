package model

import (
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
)

// 测试 AddOrUpdatePath 基本功能
func TestAddOrUpdatePath(t *testing.T) {
	// 测试数据创建
	uid := uuid.New()
	path := Path{
		Name:    "path1",
		Proxies: []string{"proxy1", "proxy2"},
		UserID:  uid,
	}

	// 添加新 Path
	err := AddOrUpdatePath(db, &path, "", false)
	assert.NoError(t, err, "should add path without error")

	// 验证 Path 插入效果
	var newPath Path
	db.Where("name = ?", "path1").First(&newPath)
	assert.Equal(t, path.Name, newPath.Name, "the inserted path should match")

	// 更新 Path
	err = AddOrUpdatePath(db, &path, "new_path1", false)
	assert.NoError(t, err, "should update path without error")

	var newPath1 Path
	// 验证 Path 更新效果
	db.Where("name = ?", "new_path1").First(&newPath1)
	assert.Equal(t, "new_path1", newPath1.Name, "the updated path should reflect the new name")
}

// 测试 GetPath
func TestGetPath(t *testing.T) {
	// 插入 Path
	uid := uuid.New()
	path := Path{
		Name:    "path1",
		Proxies: []string{"proxy1", "proxy2"},
		UserID:  uid,
	}
	db.Create(&path)

	// 获取 Path
	retrieved := GetPath(db, "path1")
	assert.Equal(t, path.Name, retrieved.Name)
	assert.Equal(t, path.UserID, retrieved.UserID)
}

// 测试通过用户获取 Path 列表
func TestGetPathsByUser(t *testing.T) {
	uid := uuid.New()
	path1 := Path{Name: "user_path1", UserID: uid}
	path2 := Path{Name: "user_path2", UserID: uid}

	// 插入多条 Path
	db.Create(&path1)
	db.Create(&path2)

	// 通过用户 uuid 获取路径
	paths := GetPathsByUser(db, uid)
	assert.Equal(t, 2, len(paths), "there should be two paths for the user")
}

// 测试 Path 使用量统计
func TestGetPathUseCount(t *testing.T) {
	// 模拟 ip_rule 记录
	rule := IpRule{Proxy: "path1"}
	db.Create(&rule)

	// 获取使用计数
	count := GetPathUseCount(db, "path1")
	assert.Equal(t, int64(1), count, "the proxy should be used once")
}

// 测试检查并更新 Path 使用状态
func TestCheckPathUse(t *testing.T) {
	// 插入 Path 和 IpRule
	path := Path{Name: "path_exclusive", Exclusive: PathExclusiveALL, ChangeTime: 50}
	rule := IpRule{Proxy: "other"}
	db.Create(&path)
	db.Create(&rule)

	// 检查并更新独占的路径
	CheckPathUse(db)

	updatedPath := GetPath(db, "path_exclusive")
	assert.Equal(t, PathExclusiveNone, updatedPath.Exclusive, "exclusive should be updated to none")
	assert.Equal(t, uint64(0), updatedPath.ChangeTime, "change time should be updated to 0")
}
