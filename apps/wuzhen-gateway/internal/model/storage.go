package model

import (
	"encoding/json"
	"errors"
	"sync"

	"gorm.io/gorm"
)

var mu sync.RWMutex

type Storage struct {
	Key   string `gorm:"comment:存储键;primary_key"`
	Value string `gorm:"comment:存储值"`
	Base
}

type StorageModel struct {
	db *gorm.DB
}

func NewStorageModel(db *gorm.DB) *StorageModel {
	return &StorageModel{
		db: db,
	}
}

func (m *StorageModel) Get(key string) (interface{}, error) {
	mu.RLock()
	defer mu.RUnlock()
	var value string
	err := m.db.Model(&Storage{}).Where("key = ?", key).Select("value").First(&value).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return "", nil
	}
	var result interface{}
	err = json.Unmarshal([]byte(value), &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (m *StorageModel) Set(key string, value interface{}) error {
	mu.Lock()
	defer mu.Unlock()
	var s Storage
	err := m.db.Model(&Storage{}).Where("key = ?", key).First(&s).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}
	if errors.Is(err, gorm.ErrRecordNotFound) {
		s.Key = key
	}
	j, err := json.Marshal(value)
	if err != nil {
		return err
	}
	s.Value = string(j)
	err = m.db.Save(&s).Error
	if err != nil {
		return err
	}
	return nil
}

func (m *StorageModel) Del(key string) error {
	mu.Lock()
	defer mu.Unlock()
	var s Storage
	err := m.db.Model(&Storage{}).Where("key = ?", key).First(&s).Error
	if err != nil {
		return err
	}
	err = m.db.Delete(&s).Error
	if err != nil {
		return err
	}
	return nil
}
