package middleware

import (
	"gateway/internal/model"
	"net/http"

	"github.com/go-chi/render"
)

func AdminAuth(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		u, ok := r.Context().Value("user").(model.User)
		if !ok {
			render.Status(r, http.StatusForbidden)
			render.JSON(w, r, map[string]interface{}{
				"msg":  "no permissions",
				"code": 403,
				"data": nil,
			})
			return
		}
		if !u.IsAdmin {
			render.Status(r, http.StatusForbidden)
			render.JSON(w, r, map[string]interface{}{
				"msg":  "no permissions",
				"code": 403,
				"data": nil,
			})
			return
		}
		next.ServeHTTP(w, r)
	})
}
