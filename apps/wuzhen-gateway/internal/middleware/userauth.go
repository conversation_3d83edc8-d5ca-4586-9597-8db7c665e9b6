package middleware

import (
	"context"
	"gateway/internal/db"
	"gateway/internal/model"
	"github.com/go-chi/render"
	"net/http"
)

func UserAuth(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 查询用户信息并设置到上下文
		var user model.User
		err := db.GetDb().First(&user).Error
		if err != nil {
			render.Status(r, http.StatusUnauthorized)
			render.JSON(w, r, map[string]interface{}{
				"msg":  "no authorization",
				"code": 401,
				"data": nil,
			})
			return
		}
		ctx := context.WithValue(r.Context(), "user", user)
		r = r.WithContext(ctx)
		next.ServeHTTP(w, r)
	})
}
