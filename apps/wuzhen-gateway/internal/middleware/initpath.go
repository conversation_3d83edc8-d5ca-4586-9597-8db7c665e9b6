package middleware

import (
	"gateway/internal/clash"
	"gateway/internal/db"
	_interface "gateway/internal/interface"
	"gateway/internal/log"
	"gateway/internal/model"
	"gateway/internal/utils"
	"net"
	"net/http"
	"sync"
	"time"

	"github.com/go-chi/render"
	"github.com/google/uuid"
)

var pathLock = &sync.Mutex{}

func InitPath(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		//add client
		ip := r.Context().Value("ip").(string)
		client := model.GetClientByIp(db.GetDb(), ip)
		if client.IP == "" {
			//add
			mac := utils.GenerateMAC(net.ParseIP(ip))
			arp, err := utils.Arp(utils.GetGatewayInterface(), ip)
			if err == nil && arp.String() != "" {
				mac = arp
			} else {
				table, _ := utils.GetARPTable()
				for _, entry := range table {
					if ip == entry.IPAddress {
						arp, err = net.ParseMAC(entry.HWAddress)
						if err == nil {
							mac = arp
						}
					}
				}
			}
			byMac := model.GetClientByMac(db.GetDb(), mac.String())
			if byMac.Mac == "" {
				db.GetDb().Create(&model.Client{
					Mac:          mac.String(),
					Hostname:     "Unknown",
					IP:           ip,
					LeaseEndTime: time.Now().AddDate(0, 0, 7).Unix(),
					Static:       true,
				})
			} else {
				byMac.IP = ip
				byMac.LeaseEndTime = time.Now().AddDate(0, 0, 7).Unix()
				db.GetDb().Save(&byMac)
			}
		}
		//规则校验
		user := r.Context().Value("user").(model.User)
		err := initPath(user.ID, ip)
		if err != nil {
			render.JSON(w, r, map[string]interface{}{
				"msg":  "init path error",
				"code": http.StatusInternalServerError,
				"data": nil,
			})
			return
		}
		next.ServeHTTP(w, r)
	})
}
func initPath(uid uuid.UUID, ip string) error {
	pathLock.Lock()
	defer pathLock.Unlock()
	getDb := db.GetDb()
	rule := model.GetIpRule(getDb, ip)
	path := model.GetPath(getDb, rule.Proxy)
	if rule.Proxy == "" || path.UserID != uid { //没有代理规则初始化一个
		//从现有链路选择
		var usePath string
		paths := model.GetPathsByUser(getDb, uid)
		//顺序选择
		for _, newPath := range paths {
			if model.CanUserProxies(getDb, newPath.Proxies...) {
				usePath = newPath.Name
				break
			}
		}
		if usePath == "" {
			newPath := _interface.ClashApi.NewInitPath(ip, uid)
			err := model.AddOrUpdatePath(db.GetDb(), newPath, "", true)
			if err != nil {
				return err
			}
			usePath = newPath.Name
		}
		err := model.AddOrUpdateIpRule(db.GetDb(), ip, usePath, false)
		if err != nil {
			return err
		}
		model.CheckPathUse(getDb)
		model.CheckProxiesUse(getDb)
		err = clash.HotUpdate()
		if err != nil {
			log.GetLogger().Errorln("host update", err)
		}
		return err
	}
	return nil
}
