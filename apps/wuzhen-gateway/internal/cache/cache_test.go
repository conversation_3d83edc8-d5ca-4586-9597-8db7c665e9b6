package cache

import (
	"testing"
	"time"
)

func TestCacheSetAndGet(t *testing.T) {
	Init(1 * time.Minute) // 初始化缓存，清理间隔为 1 分钟
	c := GetCache()

	c.Set("testKey", "testValue", time.Now().Add(1*time.Hour))
	value, found := c.Get("testKey")
	if !found {
		t.Error("Expected to find 'testKey' in the cache")
	}
	if value != "testValue" {
		t.<PERSON><PERSON>("Expected value to be 'testValue', got %v", value)
	}
}

func TestCacheExpiration(t *testing.T) {
	Init(1 * time.Minute) // 初始化缓存，清理间隔为 1 分钟
	c := GetCache()

	expireAt := time.Now().Add(1 * time.Second)
	c.Set("expireKey", "expireValue", expireAt)
	time.Sleep(2 * time.Second) // 等待一段时间，让缓存过期

	value, found := c.Get("expireKey")
	if found || value != nil {
		t.<PERSON>r("Expected 'expireKey' to be expired from the cache")
	}
}

func TestCacheCleanup(t *testing.T) {
	Init(1 * time.Second) // 初始化缓存，清理间隔为 1 秒
	c := GetCache()

	expireAt := time.Now().Add(500 * time.Millisecond) // 缓存项在 500 毫秒后过期
	c.Set("cleanupKey", "cleanupValue", expireAt)
	time.Sleep(2 * time.Second) // 等待 2 秒确保 cleanup goroutine 执行

	_, found := c.items["cleanupKey"]
	if found {
		t.Error("Expected 'cleanupKey' to be cleaned up from the cache")
	}
}
