package cache

import (
	"sync"
	"time"
)

type Item struct {
	Object   interface{}
	ExpireAt time.Time // 过期时间
}

type Cache struct {
	items           map[string]Item
	mu              sync.RWMutex
	cleanupInterval time.Duration // 清除间隔
}

var cache *Cache

func (c *Cache) Set(key string, value interface{}, expireAt time.Time) {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.items[key] = Item{
		Object:   value,
		ExpireAt: expireAt,
	}
}

func (c *Cache) Get(key string) (interface{}, bool) {
	c.mu.RLock()
	item, exists := c.items[key]
	c.mu.RUnlock()
	if !exists {
		return nil, false
	}
	// 判断是否过期
	if item.ExpireAt.Before(time.Now()) {
		return nil, false
	}
	return item.Object, true
}

func (c *Cache) clean() {
	ticker := time.NewTicker(c.cleanupInterval)
	defer ticker.Stop()
	for {
		select {
		case t := <-ticker.C:
			c.mu.Lock()
			for k, v := range c.items {
				if v.ExpireAt.Before(t) {
					delete(c.items, k)
				}
			}
			c.mu.Unlock()
		}
	}
}

func Init(cleanupInterval time.Duration) {
	cache = &Cache{
		items:           make(map[string]Item),
		cleanupInterval: cleanupInterval,
	}
	go cache.clean()
}

func GetCache() *Cache {
	return cache
}
