package parser

import (
	"gateway/internal/parser/model"
	"gateway/internal/utils"
	"strings"
)

func ParseProxy(raw string) []model.Proxy {
	base64, _ := utils.DecodeBase64(raw)
	proxies := strings.Split(base64, "\n")
	var result []model.Proxy
	for _, proxy := range proxies {
		if proxy != "" {
			var proxyItem model.Proxy
			var err error
			// 解析节点
			if strings.HasPrefix(proxy, ShadowsocksPrefix) {
				proxyItem, err = ParseShadowsocks(proxy)
			}
			if strings.HasPrefix(proxy, TrojanPrefix) {
				proxyItem, err = ParseTrojan(proxy)
			}
			if strings.HasPrefix(proxy, VMessPrefix) {
				proxyItem, err = ParseVmess(proxy)
			}
			if strings.HasPrefix(proxy, VLESSPrefix) {
				proxyItem, err = ParseVless(proxy)
			}
			if strings.HasPrefix(proxy, ShadowsocksRPrefix) {
				proxyItem, err = ParseShadowsocksR(proxy)
			}
			if strings.HasPrefix(proxy, Hysteria2Prefix1) ||
				strings.HasPrefix(proxy, Hysteria2Prefix2) {
				proxyItem, err = ParseHysteria2(proxy)
			}
			if strings.HasPrefix(proxy, HysteriaPrefix) {
				proxyItem, err = ParseHysteria(proxy)
			}
			if err == nil {
				result = append(result, proxyItem)
			}
		}
	}
	return result
}
