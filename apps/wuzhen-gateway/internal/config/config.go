package config

import (
	"crypto/md5"
	"fmt"
	"gateway/internal/utils"
	"os"
	"path/filepath"
	"sync"
	"time"

	"github.com/3th1nk/cidr"
	"github.com/spf13/viper"
)

func RemoteConfigPath() string {
	return filepath.Join(utils.GetRunDir(), "config-remote.json")
}
func FinalConfigPath() string {
	return filepath.Join(utils.GetRunDir(), "config-final.json")
}

var Version = "v1.0.0"
var BuildTime = fmt.Sprintf("%d", time.Now().Unix())
var Gateway = "**********"
var CIDR = "**********/20"

type BasicAuth struct {
	UserName string `json:"user_name" mapstructure:"user_name"`
	Password string `json:"password"  mapstructure:"password"`
}

type GatewayConfig struct {
	Dev                bool              `json:"dev"                             mapstructure:"dev"`                             // 是否是开发模式运行
	DeviceCode         string            `json:"device_code"                     mapstructure:"device_code"`                     // 设备码
	Secret             string            `json:"secret"                          mapstructure:"secret"`                          // 设备密钥
	LogLevel           string            `json:"log_level"                       mapstructure:"log_level"`                       // 日志级别
	ClashLogLevel      string            `json:"clash_log_level"                 mapstructure:"clash_log_level"`                 // clash 日志级别
	Pprof              bool              `json:"pprof"                           mapstructure:"pprof"`                           // 是否开启pprof
	JwtSecret          string            `json:"jwt_secret"                      mapstructure:"jwt_secret"`                      // jwt密钥
	ClashFilePath      string            `json:"clash_file_path"                 mapstructure:"clash_file_path"`                 // clash 文件路径
	ClashApiAddress    string            `json:"clash_api_address"               mapstructure:"clash_api_address"`               // clash api 地址
	ClashSecret        string            `json:"clash_secret"                    mapstructure:"clash_secret"`                    // clash api 密钥
	ClashProxyPort     int               `json:"clash_proxy_port"                mapstructure:"clash_proxy_port"`                // clash 代理端口
	CloudGateway       string            `json:"cloud_gateway"                   mapstructure:"cloud_gateway"`                   // 云端网关地址
	PassGfwList        []map[string]any  `json:"pass_gfw_list"                   mapstructure:"pass_gfw_list"`                   // 预置passGfw列表
	GuardList          []map[string]any  `json:"guard_list"                      mapstructure:"guard_list"`                      // 预置守护列表
	ExitList           []map[string]any  `json:"exit_list"                       mapstructure:"exit_list"`                       // 预置exit列表
	TestDelayUrl       string            `json:"test_delay_url"                  mapstructure:"test_delay_url"`                  // 测试延迟地址
	TestDelayTimeout   int               `json:"test_delay_timeout"              mapstructure:"test_delay_timeout"`              // 测试延迟超时
	CustomizationRule  []string          `json:"customization_rule"              mapstructure:"customization_rule"`              // 自定义规则
	CustomizationHosts map[string]string `json:"customization_hosts"             mapstructure:"customization_hosts"`             // 自定义Hosts
	PassGfw            string            `json:"pass_gfw"                        mapstructure:"pass_gfw"`                        // auto|yes|no
	ExclusiveNum       uint              `json:"exclusive_num"                   mapstructure:"exclusive_num"`                   // 独占数量
	Export             string            `json:"export"                          mapstructure:"export"`                          // prometheus export gateway url
	ExportLog          string            `json:"export_log"                      mapstructure:"export_log"`                      // prometheus export log url
	ExportLogBasicAuth *BasicAuth        `json:"export_log_basic_auth,omitempty" mapstructure:"export_log_basic_auth,omitempty"` // loki BasicAuth
	Domain             string            `json:"domain"                          mapstructure:"domain"`                          // 网关自定义域名
	Dsn                string            `json:"dsn"                             mapstructure:"dsn"`                             // sentry dsn
	WGEndpoint         []string          `json:"wg_endpoint"                     mapstructure:"wg_endpoint"`                     // wg endpoint
	ReportToken        bool              `json:"report_token"                    mapstructure:"report_token"`                    // 是否上报Token到平台
	DHCP               struct {
		Enable bool       `json:"enable" mapstructure:"enable"` // 是否开启dhcp
		Lease  int        `json:"lease" mapstructure:"lease"`   // 过期时间 in seconds
		Cidr   *cidr.CIDR `json:"-"`
		IfName string     `json:"if_name" mapstructure:"if_name"` // 绑定网卡 br-lan
	} `json:"dhcp"                            mapstructure:"dhcp"`
	Flags []string `json:"flags"                           mapstructure:"flags"` // 标签
}

var config = GatewayConfig{}
var confLock = sync.Mutex{}
var configPath = filepath.Join(utils.GetEtcDir(), "config-local.json")

func Config() GatewayConfig {
	confLock.Lock()
	defer confLock.Unlock()
	return config
}

func SetConfigPath(path string) {
	configPath = path
}

func LoadConfig() error {
	confLock.Lock()
	defer confLock.Unlock()

	localViper := viper.New()
	localViper.SetConfigFile(configPath)
	err := localViper.ReadInConfig()
	if err != nil {
		return err
	}
	if !localViper.IsSet("device_code") || !localViper.IsSet("secret") {
		return fmt.Errorf("device_code or secret is empty")
	}

	// 存在远程配置，则合并到本地配置
	if _, err = os.Stat(RemoteConfigPath()); err == nil {
		localViper.SetConfigFile(RemoteConfigPath())
		err = localViper.MergeInConfig()
		if err != nil {
			return err
		}
	}

	{
		// 设置默认值
		localViper.SetDefault("log_level", "info")
		localViper.SetDefault("clash_log_level", "error")
		localViper.SetDefault("jwt_secret", fmt.Sprintf("%x", md5.Sum([]byte(config.Secret))))
		localViper.SetDefault("clash_secret", "xrouter")
		localViper.SetDefault("test_delay_url", "https://cp.cloudflare.com/generate_204")
		localViper.SetDefault("test_delay_timeout", 10000)
		localViper.SetDefault("pass_gfw", "auto")
		localViper.SetDefault("exclusive_num", 10)
		localViper.SetDefault("domain", "x.net")
		localViper.SetDefault("dhcp.lease", 86400*7)
		localViper.SetDefault("dhcp.if_name", "br-lan")
		localViper.SetDefault("flags", []string{})
	}

	// 解析并保存最终配置
	if err = localViper.Unmarshal(&config); err != nil {
		return err
	}
	if err = localViper.WriteConfigAs(FinalConfigPath()); err != nil {
		return err
	}

	config.DHCP.Cidr = cidr.ParseNoError(CIDR)
	return nil
}
func HasFlag(flag string) bool {
	for _, v := range config.Flags {
		if v == flag {
			return true
		}
	}
	return false
}
