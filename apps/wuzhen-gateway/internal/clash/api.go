package clash

import (
	"context"
	"encoding/json"
	"fmt"
	"gateway/internal/config"
	"gateway/internal/db"
	_interface "gateway/internal/interface"
	"gateway/internal/model"
	"io"
	"net"
	"net/http"
	"net/netip"
	"sync"
	"time"

	"github.com/google/uuid"
	"github.com/metacubex/mihomo/adapter"
	"github.com/metacubex/mihomo/adapter/outboundgroup"
	C "github.com/metacubex/mihomo/constant"
	"github.com/metacubex/mihomo/tunnel"
	"github.com/metacubex/mihomo/tunnel/statistic"
)

var (
	_httpClient *http.Client
	once        sync.Once
)
var _clash clash

type clash struct{}

func init() {
	_clash = clash{}
	_interface.ClashApi = &_clash
}
func httpClient() *http.Client {
	once.Do(func() {
		// 创建 HTTP 客户端并设置 Transport 为 Unix 套接字
		transport := &http.Transport{
			DialContext: func(_ context.Context, _, _ string) (net.Conn, error) {
				a := unixAddr()
				return net.DialTimeout("unix", a, time.Second*10)
			},
		}
		_httpClient = &http.Client{Transport: transport}
	})
	return _httpClient
}
func (c *clash) GetProxies() map[string]C.Proxy {
	return tunnel.ProxiesWithProviders()
}
func (c *clash) Delay(name string) int {
	Type := "proxies"
	if name == model.ProxyAuto || name == model.ProxyAutoGuard {
		Type = "group"
	}
	request, err := http.NewRequest(
		http.MethodGet,
		fmt.Sprintf(
			"http://localhost/%s/%s/delay?url=%s&timeout=%d",
			Type,
			name,
			config.Config().TestDelayUrl,
			config.Config().TestDelayTimeout,
		),
		nil,
	)
	if err != nil {
		return -1
	}
	do, err := httpClient().Do(request)
	if err != nil {
		return -1
	}
	defer func() { _ = do.Body.Close() }()
	if do.StatusCode == 200 {
		var data []byte
		data, err = io.ReadAll(do.Body)
		if err != nil {
			return -1
		}
		res := make(map[string]int)
		err = json.Unmarshal(data, &res)
		if err != nil {
			return -1
		}
		d := 9999
		for _, i := range res {
			if i < d {
				d = i
			}
		}
		if d == 9999 {
			return -1
		}
		return d
	}
	return -1
}
func (c *clash) CloseSourceIpConnection(sourceIp string) {
	snapshot := statistic.DefaultManager.Snapshot()
	for _, connection := range snapshot.Connections {
		if connection.Metadata.SrcIP.String() == sourceIp {
			_ = statistic.DefaultManager.Get(connection.UUID.String()).Close()
		}
	}
}
func (c *clash) GetAutoGuardNow() string {
	if p, exists := c.GetProxies()[model.ProxyAutoGuard]; exists {
		test := p.(*adapter.Proxy)
		urlTest := test.ProxyAdapter.(*outboundgroup.URLTest)
		return urlTest.Now()
	}
	proxies := make([]*model.Proxies, 0)
	db.GetDb().Model(&model.Proxies{}).Find(&proxies)
	for _, proxy := range proxies {
		if proxy.Type == model.ProxyTypeGuardProxy {
			return proxy.Name
		}
	}
	return ""
}
func (c *clash) GetGetAutoNow() string {
	if p, exists := c.GetProxies()[model.ProxyAuto]; exists {
		test := p.(*adapter.Proxy)
		urlTest := test.ProxyAdapter.(*outboundgroup.URLTest)
		return urlTest.Now()
	}
	proxies := make([]*model.Proxies, 0)
	db.GetDb().Model(&model.Proxies{}).Find(&proxies)
	for _, proxy := range proxies {
		if proxy.Type == model.ProxyTypeExitProxy {
			return proxy.Name
		}
	}
	return ""
}
func (c *clash) GetAutoPassGFWNow() (string, bool) {
	if p, exists := c.GetProxies()[model.ProxyTypePassGfw]; exists {
		test := p.(*adapter.Proxy)
		urlTest := test.ProxyAdapter.(*outboundgroup.URLTest)
		return urlTest.Now(), true
	}
	// 刚刚启动时可能还没有测出最快的节点，此处从数据库取第一个
	proxies := make([]*model.Proxies, 0)
	db.GetDb().Model(&model.Proxies{}).Find(&proxies)
	for _, proxy := range proxies {
		if proxy.Type == model.ProxyTypePassGfw {
			return proxy.Name, false
		}
	}
	return "", false
}
func (c *clash) GetAutoPath() string {
	path := ""
	if p, exists := c.GetProxies()[model.ProxyTypePassGfw]; exists {
		test := p.(*adapter.Proxy)
		urlTest := test.ProxyAdapter.(*outboundgroup.URLTest)
		path += urlTest.Now()
	}
	if p, exists := c.GetProxies()[model.ProxyAutoGuard]; exists {
		test := p.(*adapter.Proxy)
		urlTest := test.ProxyAdapter.(*outboundgroup.URLTest)
		path += urlTest.Now()
	}
	if p, exists := c.GetProxies()[model.ProxyAuto]; exists {
		test := p.(*adapter.Proxy)
		urlTest := test.ProxyAdapter.(*outboundgroup.URLTest)
		path += urlTest.Now()
	}
	return path
}
func (c *clash) NewInitPath(ip string, userid uuid.UUID) *model.Path {
	passgfw, _ := c.GetAutoPassGFWNow()
	guard := c.GetAutoGuardNow()
	exit := c.GetGetAutoNow()
	return &model.Path{
		Name:      fmt.Sprintf("default(%s)-%s", ip, uuid.New().String()[0:4]),
		Proxies:   []string{passgfw, guard, exit},
		Exclusive: model.PathExclusiveNone,
		UserID:    userid,
	}
}

func (c *clash) GetConnectCount() map[netip.Addr]int {
	snapshot := statistic.DefaultManager.Snapshot()
	var resp = make(map[netip.Addr]int)
	for _, connection := range snapshot.Connections {
		if connection.Metadata.SrcIP.IsValid() {
			resp[connection.Metadata.SrcIP] += 1
		}
	}
	return resp
}
