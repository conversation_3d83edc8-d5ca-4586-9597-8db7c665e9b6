package clash

import (
	"encoding/json"
	"errors"
	"fmt"
	"gateway/internal/config"
	"gateway/internal/db"
	_interface "gateway/internal/interface"
	"gateway/internal/log"
	"gateway/internal/model"
	"golang.org/x/exp/maps"
	"golang.org/x/exp/slices"
	"math/rand/v2"
	"net"
	"net/netip"
	"net/url"
	"os"
	"strings"
	"sync"
	"time"

	"github.com/metacubex/mihomo/common/atomic"
	clashconfig "github.com/metacubex/mihomo/config"
	"github.com/metacubex/mihomo/constant"
	"github.com/metacubex/mihomo/hub"
	"github.com/metacubex/mihomo/hub/executor"
	"github.com/metacubex/mihomo/hub/route"
	clashlog "github.com/metacubex/mihomo/log"
	"github.com/metacubex/mihomo/tunnel"
	"gopkg.in/yaml.v3"
	"gorm.io/gorm"
)

var TunAddr = "**********"
var unixName = "xrouter_unix.sock"
var clashRawConfig *clashconfig.RawConfig
var configLock = &sync.Mutex{}
var startFlag atomic.Bool

func unixAddr() string {
	return constant.Path.Resolve(unixName)
}

// 复制 map[string]any 的函数
func copyMap(original map[string]any) map[string]any {
	newMap := make(map[string]any)
	for key, value := range original {
		newMap[key] = value // 直接复制值
	}
	return newMap
}

var (
	groupMaxProxy   = 15
	countryPriority = []string{"hk", "jp", "sg", "tw", "us", "kr", "other"}
)

// autoProxyNameFilter 为代理组筛选代理节点，
func autoProxyNameFilter(list []map[string]any) (nameList []string) {
	var countrySet = make(map[string][]map[string]any)
	for _, v := range list {
		if country, ok := v["country_code"].(string); ok {
			if _, ok := countrySet[country]; !ok {
				countrySet[country] = make([]map[string]any, 0)
			}
			countrySet[country] = append(countrySet[country], v)
		}
	}

	nameList = make([]string, 0)
	for _, countryCode := range countryPriority {
		nameList = nameListAppend(nameList, countrySet, countryCode)
		if len(nameList) >= groupMaxProxy {
			break
		}
	}
	return nameList
}

func nameListAppend(
	nameList []string,
	countrySet map[string][]map[string]any,
	countryCode string,
) []string {
	list := make([]map[string]any, 0)
	if countryCode == "other" {
		// 从剩余所有国家中获取
		selectedCountries := countryPriority[:len(countryPriority)-1]
		allCountries := maps.Keys(countrySet)
		remainedCounties := make([]string, 0)
		for _, country := range allCountries {
			if !slices.Contains(selectedCountries, country) {
				remainedCounties = append(remainedCounties, country)
			}
		}

		for _, country := range remainedCounties {
			list = append(list, countrySet[country]...)
		}
	} else {
		var exists bool
		list, exists = countrySet[countryCode]
		if !exists {
			return nameList
		}
	}

	if len(nameList)+len(list) > groupMaxProxy {
		// 随机获取到最大限制
		keys := rand.Perm(len(list))
		for _, key := range keys[:groupMaxProxy-len(nameList)] {
			nameList = append(nameList, list[key]["name"].(string))
		}
	} else {
		for _, v := range list {
			nameList = append(nameList, v["name"].(string))
		}
	}
	return nameList
}

func newConfig(
	passGfwLink, guardLink, exitLink []map[string]any,
	rules []*model.IpRule,
	paths []*model.Path,
) (*clashconfig.RawConfig, error) {
	if len(passGfwLink) == 0 {
		return nil, errors.New("passGfw is empty")
	}
	if len(guardLink) == 0 {
		return nil, errors.New("guardLink is empty")
	}
	if len(exitLink) == 0 {
		return nil, errors.New("exitLink is empty")
	}
	//基础规则配置
	template := newTemplate()

	proxyMap := make(map[string]map[string]any)

	for i, v := range passGfwLink {
		if name, ok2 := v["name"].(string); ok2 {
			passGfwLink[i]["proxy_type"] = model.ProxyTypePassGfw
			proxyMap[name] = passGfwLink[i]
		}
	}
	passGfwNameList := autoProxyNameFilter(passGfwLink)
	passGfw := model.ProxyTypePassGfw
	template.ProxyGroup = append(template.ProxyGroup, map[string]any{
		"name":             passGfw,
		"type":             "url-test",
		"proxies":          passGfwNameList,
		"url":              config.Config().TestDelayUrl,
		"interval":         10,
		"tolerance":        100,
		"timeout":          config.Config().TestDelayTimeout,
		"lazy":             false,
		"max-failed-times": 1,
	})

	//守卫

	for i, v := range guardLink {
		if name, ok2 := v["name"].(string); ok2 {
			guardLink[i]["dialer-proxy"] = passGfw
			guardLink[i]["proxy_type"] = model.ProxyTypeGuardProxy
			proxyMap[name] = guardLink[i]
		}
	}
	guardNameList := autoProxyNameFilter(guardLink)
	//最优守卫
	template.ProxyGroup = append(template.ProxyGroup, map[string]any{
		"name":             model.ProxyAutoGuard,
		"type":             "url-test",
		"proxies":          guardNameList,
		"url":              config.Config().TestDelayUrl,
		"interval":         10,
		"tolerance":        100,
		"timeout":          config.Config().TestDelayTimeout,
		"lazy":             true,
		"max-failed-times": 1,
	})
	//出口
	for i, v := range exitLink {
		if name, ok2 := v["name"].(string); ok2 {
			exitLink[i]["dialer-proxy"] = model.ProxyAutoGuard
			exitLink[i]["proxy_type"] = model.ProxyTypeExitProxy
			proxyMap[name] = exitLink[i]
		}
	}
	exitNameList := autoProxyNameFilter(exitLink)

	template.Proxy = append(template.Proxy, passGfwLink...)
	template.Proxy = append(template.Proxy, guardLink...)
	template.Proxy = append(template.Proxy, exitLink...)
	template.ProxyGroup = append(template.ProxyGroup, map[string]any{
		"name":             model.ProxyAuto,
		"type":             "url-test",
		"proxies":          exitNameList,
		"url":              config.Config().TestDelayUrl,
		"interval":         10,
		"tolerance":        100,
		"timeout":          config.Config().TestDelayTimeout,
		"lazy":             true,
		"max-failed-times": 1,
	})
	template.ProxyGroup = append(template.ProxyGroup, map[string]any{
		"name":             model.ProxyBalance,
		"type":             "load-balance",
		"proxies":          exitNameList,
		"url":              config.Config().TestDelayUrl,
		"interval":         30,
		"tolerance":        100,
		"timeout":          config.Config().TestDelayTimeout,
		"lazy":             false,
		"max-failed-times": 1,
		"strategy":         "src-sessions",
	})
	//获取主从恢复
	getFallback := func(name, main, dialer string) map[string]any {
		proxies := make([]map[string]any, 0)
		proxiesNames := make([]string, 0)
		//主节点
		mainProxy := copyMap(proxyMap[main])
		mainProxy["dialer-proxy"] = dialer
		mainProxy["name"] = fmt.Sprintf("fallback_%s_main", name)
		proxies = append(proxies, mainProxy)
		proxiesNames = append(proxiesNames, mainProxy["name"].(string))
		mainCode := ""
		if code, ok := mainProxy["country_code"].(string); ok {
			mainCode = code
		}
		//从节点 随机一个同地区同类型
		if mainCode != "" {
			for k, v := range proxyMap {
				if bName, ok := v["name"].(string); ok && bName == main {
					continue
				}
				if code, ok := v["country_code"].(string); ok {
					if mainProxy["proxy_type"] == v["proxy_type"] && code == mainCode &&
						v["use_status"] != model.ProxyUseStatusExclusive &&
						v["use_status"] != model.ProxyUseStatusDiscard {
						backupProxy := copyMap(proxyMap[k])
						backupProxy["name"] = fmt.Sprintf("fallback_%s_backup", name)
						backupProxy["dialer-proxy"] = dialer
						proxies = append(proxies, backupProxy)
						proxiesNames = append(proxiesNames, backupProxy["name"].(string))
						break
					}
				}
			}
		}
		for _, proxy := range proxies {
			template.Proxy = append(template.Proxy, proxy)
		}
		fallback := map[string]any{
			"name":             name,
			"type":             "fallback",
			"proxies":          proxiesNames,
			"url":              config.Config().TestDelayUrl,
			"interval":         5,
			"timeout":          config.Config().TestDelayTimeout,
			"lazy":             true,
			"max-failed-times": 1,
		}
		return fallback
	}
	//链路
	for _, path := range paths {
		previous := ""
		for i, proxy := range path.Proxies {
			if _, ok := proxyMap[proxy]; !ok {
				break
			}
			name := fmt.Sprintf("path_%s_%d", path.Name, i+1)
			if i == len(path.Proxies)-1 {
				//最后一个代理 也就是出口
				template.ProxyGroup = append(
					template.ProxyGroup,
					getFallback(path.Name, proxy, previous),
				)
			} else {
				template.ProxyGroup = append(template.ProxyGroup, getFallback(name, proxy, previous))
				previous = name
			}
		}
	}
	var ruleArray []string
	//禁用规则
	ruleArray = append(ruleArray, "AND,((DST-PORT,443),(NETWORK,UDP)),REJECT")

	raw := config.Config().CloudGateway
	if !strings.HasPrefix(raw, "http://") && !strings.HasPrefix(raw, "https://") {
		raw = "https://" + raw
	}
	u, err := url.Parse(raw)
	if err == nil {
		host := u.Hostname()
		ruleArray = append(ruleArray, fmt.Sprintf("DOMAIN-SUFFIX,%s,DIRECT", host))
	}

	// 内网规则
	//ruleArray = append(ruleArray, "IP-CIDR,***********/16,DIRECT")
	//ruleArray = append(ruleArray, "IP-CIDR,10.0.0.0/8,DIRECT")
	//ruleArray = append(ruleArray, "IP-CIDR,**********/12,DIRECT")
	ruleArray = append(ruleArray, "IP-CIDR,*********/8,DIRECT")
	//加入规则
	ruleArray = append(ruleArray, template.Rule...)
	//规则自定义
	for _, s := range config.Config().CustomizationRule {
		ruleArray = append(ruleArray, s)
	}

	// ip 规则
	for _, v := range rules {
		if net.ParseIP(v.IP) != nil {
			if !v.Wg {
				ruleArray = append(ruleArray, fmt.Sprintf("SRC-IP-CIDR,%s/32,%s", v.IP, v.Proxy))
			}
		}
	}
	ruleArray = append(ruleArray, fmt.Sprintf("MATCH,%s", model.ProxyBalance))
	//加入规则
	template.Rule = ruleArray
	return template, nil
}
func newTemplate() *clashconfig.RawConfig {
	defaultNameserver := "*******"
	if _interface.StatusApi.GetPassGfw() {
		defaultNameserver = "*********"
	}
	return &clashconfig.RawConfig{
		ExternalController:     config.Config().ClashApiAddress,
		Secret:                 config.Config().ClashSecret,
		MixedPort:              config.Config().ClashProxyPort,
		ExternalControllerUnix: unixName,
		Profile: clashconfig.Profile{
			StoreSelected: true,
			StoreFakeIP:   false,
		},
		IPv6:         false,
		BindAddress:  "127.0.0.1",
		Mode:         tunnel.Rule,
		UnifiedDelay: true,
		// 屏蔽clash日志输出，由myLog接管
		LogLevel:   clashlog.SILENT,
		Rule:       []string{},
		Proxy:      []map[string]any{},
		ProxyGroup: []map[string]any{},
		//FindProcessMode: process.FindProcessOff,
		GlobalUA: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
		Tun: clashconfig.RawTun{
			Enable:              true,
			Device:              "tun0",
			Stack:               constant.TunSystem,
			AutoRoute:           true,
			AutoDetectInterface: true,
			DNSHijack:           []string{"any:53"},
			StrictRoute:         true,
			Inet4RouteAddress: []netip.Prefix{
				netip.MustParsePrefix("************/32"),
			},
		},
		DNS: clashconfig.RawDNS{
			Enable:         true,
			UseHosts:       true,
			UseSystemHosts: false,
			IPv6:           false,
			Listen:         "0.0.0.0:53",
			EnhancedMode:   constant.DNSNormal,
			FakeIPRange:    fmt.Sprintf("%s/32", TunAddr), //tun ip 默认参考
			RespectRules:   true,
			DefaultNameserver: []string{ //默认 DNS, 用于解析 DNS 服务器的域名必须为 IP, 可为加密 DNS
				defaultNameserver,
			},
			NameServer: []string{ //默认的域名解析服务器，如不配置 fallback/proxy-server-nameserver , 则所有域名都由 nameserver 解析
				//"https://dns.mullvad.net/dns-query",
				//"tls://***********",
				"https://cloudflare-dns.com/dns-query",
				"tls://*******",
				"*******",
			},
			ProxyServerNameserver: []string{ //代理节点域名解析服务器，仅用于解析代理节点的域名
				defaultNameserver,
			},
			Fallback: []string{ //后备域名解析服务器，一般情况下使用境外 DNS, 保证结果可信
				//"tls://*******",
			},
		},
	}
}
func Start() error {
	dir, err := os.Getwd()
	if err != nil {
		return err
	}
	constant.SetHomeDir(dir)
	var options []hub.Option
	if config.Config().ClashApiAddress != "" {
		options = append(options, hub.WithExternalController(config.Config().ClashApiAddress))
		options = append(options, hub.WithSecret(config.Config().ClashSecret))
	}
	rawConfig, err := clashconfig.ParseRawConfig(clashRawConfig)
	if err != nil {
		return err
	}
	for _, option := range options {
		option(rawConfig)
	}
	if rawConfig.General.ExternalController != "" {
		go route.Start(
			rawConfig.General.ExternalController,
			rawConfig.General.ExternalControllerTLS,
			rawConfig.General.Secret,
			rawConfig.TLS.Certificate,
			rawConfig.TLS.PrivateKey,
			rawConfig.General.ExternalDohServer,
			rawConfig.General.LogLevel == clashlog.DEBUG,
		)
	}
	if rawConfig.General.ExternalControllerUnix != "" {
		go route.StartUnix(
			rawConfig.General.ExternalControllerUnix,
			rawConfig.General.ExternalDohServer,
			rawConfig.General.LogLevel == clashlog.DEBUG,
		)
	}
	executor.ApplyConfig(rawConfig, true)
	go syncDelay()
	startFlag.Store(true)
	go func() {
		last := _interface.StatusApi.GetPassGfw()
		for {
			load := _interface.StatusApi.GetPassGfw()
			if last != load {
				last = load
				err = HotUpdate()
				if err != nil {
					log.GetLogger().Errorln("HotUpdate err:", err)
				}
			}
			time.Sleep(time.Second * 30)
		}
	}()
	return HotUpdate()
}

func checkAndUpdate(db *gorm.DB, proxies []map[string]any, t string, delayMap map[string]int64) error {
	if len(proxies) > 0 {
		db.Where("type = ? AND use_status = ? AND source =?", t, model.ProxyUseStatusNone, model.ProxySourcePlatform).
			Delete(&model.Proxies{})
		for _, m := range proxies {
			// 验证必要字段
			if _, ok := m["name"].(string); !ok {
				continue
			}
			if _, ok := m["port"].(float64); !ok {
				continue
			}
			if _, ok := m["type"].(string); !ok {
				continue
			}
			if _, ok := m["server"].(string); !ok {
				continue
			}
			if _, ok := m["country_name"].(string); !ok {
				continue
			}
			if _, ok := m["country_code"].(string); !ok {
				continue
			}
			if _, ok := m["city_name"].(string); !ok {
				continue
			}
			marshal, err := json.Marshal(m["config"])
			if err != nil {
				continue
			}
			var c map[string]any
			err = json.Unmarshal(marshal, &c)
			if err != nil {
				continue
			}
			proxy := model.GetProxy(db, m["name"].(string))
			// 添加或更新代理
			err = model.AddOrUpdateProxies(db, &model.Proxies{
				Name:        m["name"].(string),
				Server:      m["server"].(string),
				Port:        int64(m["port"].(float64)),
				Type:        t,
				Protocol:    m["type"].(string),
				CountryName: m["country_name"].(string),
				CountryCode: m["country_code"].(string),
				CityName:    m["city_name"].(string),
				Delay:       delayMap[m["name"].(string)],
				Raw:         m,
				UseStatus:   proxy.UseStatus,
				Config:      c,
			})
			if err != nil {
				return err
			}
		}
	}
	return nil
}

func UpdateProxyList(
	db *gorm.DB,
	PassGfwProxies, GuardProxies, ExitProxies []map[string]interface{},
) {
	proxies := make([]*model.Proxies, 0)
	db.Model(&model.Proxies{}).Find(&proxies)
	dMap := make(map[string]int64)
	for _, proxy := range proxies {
		dMap[proxy.Name] = proxy.Delay
	}
	err := checkAndUpdate(db, PassGfwProxies, model.ProxyTypePassGfw, dMap)
	if err != nil {
		log.GetLogger().Errorf("checkAndUpdate %s error: %s", model.ProxyTypePassGfw, err.Error())
		return
	}
	err = checkAndUpdate(db, GuardProxies, model.ProxyTypeGuardProxy, dMap)
	if err != nil {
		log.GetLogger().Errorf("checkAndUpdate %s error: %s", model.ProxyTypeGuardProxy, err.Error())
		return
	}
	err = checkAndUpdate(db, ExitProxies, model.ProxyTypeExitProxy, dMap)
	if err != nil {
		log.GetLogger().Errorf("checkAndUpdate %s error: %s", model.ProxyTypeExitProxy, err.Error())
		return
	}
	check()
}
func check() {
	getDb := db.GetDb()
	rules := make([]*model.IpRule, 0)
	paths := make([]*model.Path, 0)
	proxies := make([]*model.Proxies, 0)
	getDb.Model(&model.Path{}).Find(&paths)
	getDb.Model(&model.Proxies{}).Find(&proxies)
	getDb.Model(&model.IpRule{}).Find(&rules)
	set := make(map[string]struct{})
	for _, p := range proxies {
		set[p.Name] = struct{}{}
	}
	for _, p := range paths {
		set[p.Name] = struct{}{}
	}
	for _, p := range rules {
		if _, ok := set[p.Proxy]; !ok {
			_ = model.DeleteIpRule(getDb, p.IP)
		}
	}
}
func Load() error {
	configLock.Lock()
	defer configLock.Unlock()
	getDb := db.GetDb()
	if len(config.Config().ExitList) > 0 || len(config.Config().PassGfwList) > 0 ||
		len(config.Config().GuardList) > 0 {
		UpdateProxyList(
			getDb,
			config.Config().PassGfwList,
			config.Config().GuardList,
			config.Config().ExitList,
		)
	}
	rules := make([]*model.IpRule, 0)
	db.GetDb().Model(&model.IpRule{}).Find(&rules)
	err := generate(rules)
	return err
}
func HotUpdate() error {
	configLock.Lock()
	defer configLock.Unlock()
	if !startFlag.Load() {
		return errors.New("not start")
	}
	rules := make([]*model.IpRule, 0)
	db.GetDb().Model(&model.IpRule{}).Find(&rules)
	err := generate(rules)
	if err != nil {
		return err
	}
	if cfg, err2 := clashconfig.ParseRawConfig(clashRawConfig); err2 == nil {
		executor.ApplyConfig(cfg, false)
		return nil
	} else {
		log.GetLogger().Errorf("Parse config error: %s", err2.Error())
		return err2
	}
}
func generate(rules []*model.IpRule) error {
	proxies := make([]*model.Proxies, 0)
	passGfwList := make([]map[string]any, 0)
	guardList := make([]map[string]any, 0)
	exitList := make([]map[string]any, 0)

	paths := make([]*model.Path, 0)
	db.GetDb().Model(&model.Proxies{}).Find(&proxies)
	db.GetDb().Model(&model.Path{}).Where("name IN (SELECT proxy FROM ip_rule)").Find(&paths)
	for _, proxy := range proxies {
		if _, ok := proxy.Raw["type"]; !ok {
			continue
		}
		item := map[string]any{
			"server":       proxy.Server,
			"port":         proxy.Port,
			"type":         proxy.Protocol,
			"name":         proxy.Name,
			"proxy_type":   proxy.Type,
			"country_code": proxy.CountryCode,
		}
		for k, v := range proxy.Config {
			item[k] = v
		}
		switch proxy.Type {
		case model.ProxyTypeExitProxy:
			exitList = append(exitList, item)
		case model.ProxyTypeGuardProxy:
			guardList = append(guardList, item)
		case model.ProxyTypePassGfw:
			passGfwList = append(passGfwList, item)
		}
	}
	temp, err := newConfig(passGfwList, guardList, exitList, rules, paths)
	if err == nil {
		clashRawConfig = temp
		if config.Config().ClashFilePath != "" {
			out, _ := yaml.Marshal(clashRawConfig)
			_ = os.WriteFile(config.Config().ClashFilePath, out, 0644)
		}
	}
	return err
}
func Config() []byte {
	out, _ := yaml.Marshal(clashRawConfig)
	return out
}
