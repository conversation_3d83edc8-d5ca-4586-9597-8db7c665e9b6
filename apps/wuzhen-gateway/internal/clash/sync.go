package clash

import (
	"gateway/internal/db"
	"gateway/internal/model"
	"sync"
	"time"
)

type delayResp struct {
	proxy *model.Proxies
	delay int
}

func syncDelay() {
	go func() {
		for {
			proxies := make([]*model.Proxies, 0)
			db.GetDb().Model(&model.Proxies{}).
				Where("type IN (?)", []string{model.ProxyTypePassGfw, model.ProxyTypeGuardProxy, model.ProxyTypeExitProxy}).
				Find(&proxies)

			delayAndUpdate(proxies)
			time.Sleep(time.Minute)
		}
	}()
}

func delayAndUpdate(proxies []*model.Proxies) {
	var wg sync.WaitGroup
	respChan := make(chan delayResp, len(proxies))

	for _, proxy := range proxies {
		wg.Add(1)
		go func(p *model.Proxies) {
			defer wg.Done()
			delay := _clash.Delay(p.Name)
			if delay == -1 {
				delay = _clash.Delay(p.Name)
			}
			respChan <- delayResp{p, delay}
		}(proxy)
	}

	go func() {
		wg.Wait()
		close(respChan)
	}()

	for resp := range respChan {
		db.GetDb().Model(resp.proxy).Update("delay", resp.delay)
	}
}
