package wgManager

import (
	"gateway/internal/clash"
	_interface "gateway/internal/interface"
	"gateway/internal/log"
	"gateway/internal/utils"
	"net"
	"net/http"
	"sync"
	"syscall"
	"time"

	"golang.org/x/sys/unix"
)

type Wg struct {
	interfaceName string
	tableID       uint64
	client        *http.Client
}

type WgManager struct {
	interval time.Duration // 检测间隔
	wgList   []Wg
}

func New(interval time.Duration) (*WgManager, error) {
	// 默认创建的wg interface
	wgList := []Wg{
		{interfaceName: "wg0", tableID: 102},
		{interfaceName: "wg1", tableID: 103},
	}
	// 循环创建 httpClient
	for i, wg := range wgList {
		client, err := newHTTPClientWithInterface(wg.interfaceName)
		if err != nil {
			return nil, err
		}
		wgList[i].client = client
	}
	_interface.StatusApi.SetWgIPRuleTableID(wgList[0].tableID) // 设置默认tableID
	return &WgManager{
		interval: interval,
		wgList:   wgList,
	}, nil
}

type interfaceDelay struct {
	interfaceName string
	delay         int64
	tableID       uint64
}

func (w *WgManager) Start() {
	ticker := time.NewTicker(w.interval)
	for {
		<-ticker.C
		var wait sync.WaitGroup
		resultChan := make(chan interfaceDelay, 2)
		for _, wg := range w.wgList {
			wait.Add(1)
			go func() {
				defer func() {
					wait.Done()
				}()
				result := interfaceDelay{
					interfaceName: wg.interfaceName,
					delay:         -1,
					tableID:       wg.tableID,
				}
				start := time.Now()
				resp, err := wg.client.Head("http://cp.cloudflare.com/generate_204")
				if err != nil {
					resultChan <- result
					return
				}
				defer resp.Body.Close()
				result.delay = time.Since(start).Milliseconds()
				resultChan <- result
			}()
		}
		wait.Wait()
		close(resultChan)
		resultMap := make(map[uint64]int64)
		for result := range resultChan {
			resultMap[result.tableID] = result.delay
		}
		currentTableID := _interface.StatusApi.GetWgIPRuleTableID()
		if resultMap[currentTableID] == -1 {
			// 当前使用的Wg超时, 检查是否有可用的wg
			for tableID, delay := range resultMap {
				if tableID != currentTableID && delay > 0 {
					_interface.StatusApi.SetWgIPRuleTableID(tableID)
					err := clash.HotUpdate()
					if err != nil {
						log.GetLogger().Errorf("Hot update proxy error: %s", err.Error())
					}
					err = utils.DeleteIPRuleFormTableID(int(tableID))
					if err != nil {
						log.GetLogger().Errorf("del ip rule error: %v", err)
					}
					break
				}
			}
		}
	}
}

// newHTTPClientWithInterface 创建一个绑定到特定网络接口的 HTTP 客户端
func newHTTPClientWithInterface(ifaceName string) (*http.Client, error) {
	d := &net.Dialer{
		Control: func(network, address string, c syscall.RawConn) error {
			return setSocketOptions(address, c, ifaceName)
		},
	}
	// 创建一个自定义的 Transport，使用自定义的 Dialer
	transport := &http.Transport{
		DialContext: d.DialContext,
	}

	// 创建一个 HTTP 客户端，使用自定义的 Transport
	client := &http.Client{
		Transport: transport,
		Timeout:   10 * time.Second,
	}

	return client, nil
}

func setSocketOptions(address string, c syscall.RawConn, interfaceName string) (err error) {
	if interfaceName == "" {
		return
	}
	var innerErr error
	err = c.Control(func(fd uintptr) {
		host, _, _ := net.SplitHostPort(address)
		if ip := net.ParseIP(host); ip != nil && !ip.IsGlobalUnicast() {
			return
		}
		if interfaceName != "" {
			if innerErr = unix.BindToDevice(int(fd), interfaceName); innerErr != nil {
				return
			}
		}
	})
	if innerErr != nil {
		err = innerErr
	}
	return
}
