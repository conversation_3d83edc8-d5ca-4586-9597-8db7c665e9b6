package utils

import (
	"bufio"
	"context"
	"fmt"
	"math/rand"
	"net"
	"net/http"
	"os"
	"os/exec"
	"strings"
	"time"
)

type NetLink struct {
	Name         string
	OperState    string
	HardwareAddr string
}

func Delay(timeout time.Duration, url string) (time.Duration, error) {
	// 创建一个带有超时的 Context
	client := http.Client{}
	client.Timeout = timeout
	defer client.CloseIdleConnections()
	// 记录请求开始时间
	start := time.Now()
	resp, err := client.Get(url)
	if err != nil {
		return -1, err
	}
	defer func() { _ = resp.Body.Close() }()
	// 记录请求结束时间
	// 计算延迟
	d := time.Since(start)
	//fmt.Printf("url: %s, delay: %v\n", url, d)
	return d, err
}
func HttpClientByInterface(timeout time.Duration, iface, dns string) *http.Client {
	client := http.Client{}
	client.Timeout = timeout
	client.Transport = &http.Transport{
		DialContext: func(ctx context.Context, network, addr string) (net.Conn, error) {
			// 获取指定名称的网卡接口
			_iface, err := net.InterfaceByName(iface)
			if err != nil {
				return nil, fmt.Errorf("could not get interface: %v", err)
			}
			// 获取网卡的第一个 IPv4 地址
			addrs, err := _iface.Addrs()
			if err != nil {
				return nil, fmt.Errorf("could not get addresses for interface: %v", err)
			}
			var localAddr net.Addr
			for _, _addr := range addrs {
				ipNet, ok := _addr.(*net.IPNet)
				if ok && ipNet.IP.To4() != nil {
					localAddr = &net.TCPAddr{
						IP: ipNet.IP.To4(),
					}
					break
				}
			}
			if localAddr == nil {
				return nil, fmt.Errorf("no suitable IP address found for interface %s", iface)
			}
			// 创建连接并绑定到指定的本地地址
			dialer := &net.Dialer{
				LocalAddr: localAddr,
				Timeout:   timeout,
			}
			if dns != "" {
				dialer.Resolver = &net.Resolver{
					PreferGo: true,
					Dial: func(ctx context.Context, network, address string) (net.Conn, error) {
						dnsServer := fmt.Sprintf("%s:53", dns)
						return net.Dial("udp", dnsServer)
					},
				}
			}
			return dialer.DialContext(ctx, network, addr)
		},
	}
	return &client
}
func DelayTun0(timeout time.Duration, url string) (time.Duration, error) {
	// 创建一个带有超时的 Context
	client := HttpClientByInterface(timeout, "br-lan", "**********")
	// 记录请求开始时间
	start := time.Now()
	resp, err := client.Get(url)
	if err != nil {
		return -1, err
	}
	defer resp.Body.Close()
	// 记录请求结束时间
	end := time.Now()
	// 计算延迟
	d := end.Sub(start)
	//fmt.Printf("delay: %v\n", d)
	return d, err
}
func GetInterfaceIP(interfaceName string) string {
	routes, err := net.Interfaces()
	if err != nil {
		return ""
	}
	for _, iface := range routes {
		addrs, err := iface.Addrs()
		if err != nil {
			continue
		}
		for _, addr := range addrs {
			if ipNet, ok := addr.(*net.IPNet); ok && ipNet.IP.To4() != nil {
				if iface.Name == interfaceName {
					return ipNet.IP.To4().String()
				}
			}
		}
	}
	return ""
}
func GetInterfaceMAC(interfaceName string) string {
	routes, err := net.Interfaces()
	if err != nil {
		return ""
	}
	for _, iface := range routes {
		if iface.Name == interfaceName {
			return iface.HardwareAddr.String()
		}
	}
	return "0:0:0:0:0:0"
}
func GetGatewayInterface() string {
	if GetInterfaceIP("br-wan") != "" {
		return "br-wan"
	}
	gateway, _ := GetDefaultGateway()
	if gateway == "" {
		wan0 := GetWan0()
		if GetInterfaceIP(wan0) != "" {
			return wan0
		}
	}
	return gateway
}

// 将 net.IP 转换为一个无符号整数
func ipToInt(ip net.IP) uint32 {
	return uint32(ip[12])<<24 + uint32(ip[13])<<16 + uint32(ip[14])<<8 + uint32(ip[15])
}

// 将无符号整数转换为 net.IP
func intToIP(i uint32) net.IP {
	return net.IP{byte(i >> 24), byte(i >> 16), byte(i >> 8), byte(i)}
}

// RandomIPInRange 在给定的 IP 范围内生成随机 IP
func RandomIPInRange(begin, end net.IP) net.IP {
	startInt := ipToInt(begin)
	endInt := ipToInt(end)
	randomInt := startInt + rand.Uint32()%(endInt-startInt+1)
	return intToIP(randomInt)
}
func GetDns() []string {
	file, err := os.Open("/etc/resolv.conf")
	if err != nil {
		fmt.Println("Error opening resolv.conf:", err)
		return []string{}
	}
	defer func() { _ = file.Close() }()
	// 创建一个 scanner 读取文件内容
	scanner := bufio.NewScanner(file)
	// 存储DNS服务器的切片
	var dnsServers []string
	// 逐行读取文件
	for scanner.Scan() {
		line := scanner.Text()
		// 只处理以 nameserver 开头的行
		if strings.HasPrefix(line, "nameserver") {
			// 分割行并获取DNS地址
			parts := strings.Fields(line)
			if len(parts) == 2 {
				dnsServers = append(dnsServers, parts[1])
			}
		}
	}
	// 检查是否有错误
	if err = scanner.Err(); err != nil {
		return []string{}
	}
	return dnsServers
}
func GetDefaultGateway() (string, error) {
	// 获取路由信息
	cmd := exec.Command("ip", "route")
	output, err := cmd.Output()
	if err != nil {
		return "", err
	}
	// 解析路由信息
	lines := strings.Split(string(output), "\n")
	for _, line := range lines {
		if strings.HasPrefix(line, "default") {
			// 获取网卡名
			parts := strings.Fields(line)
			if len(parts) >= 5 {
				return parts[4], nil // 网卡名称通常在第五个字段
			}
		}
	}
	return "", fmt.Errorf("defaultRouteNotFound")
}
func GetWan0() string {
	is, _ := net.Interfaces()
	for _, i := range is {
		if strings.HasPrefix(i.Name, "ww") {
			return i.Name
		}
	}
	return "wwan0"
}
