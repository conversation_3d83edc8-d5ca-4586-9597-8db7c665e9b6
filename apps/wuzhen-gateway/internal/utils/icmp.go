package utils

import (
	"time"

	"github.com/go-ping/ping"
)

func Ping(addr string) int {
	pinger, err := ping.NewPinger(addr)
	if err != nil {
		return -1
	}
	pinger.Timeout = time.Second
	pinger.SetPrivileged(true)
	pinger.Count = 3
	err = pinger.Run() // Blocks until finished.
	if err != nil {
		return -1
	}
	stats := pinger.Statistics()
	if stats.PacketsRecv == 0 {
		return -1
	}
	return int(stats.AvgRtt.Milliseconds())
}
