package utils

import (
	"os"
	"runtime"
)

// GetRunDir /run/xrouter，不存在时创建
func GetRunDir() string {
	var dirPath string
	switch runtime.GOOS {
	case "windows":
		homeDir, _ := os.UserHomeDir()
		dirPath = homeDir + "\\AppData\\Local\\Temp\\xrouter"
	case "linux":
		dirPath = "/run/xrouter"
	case "darwin":
		dirPath = "/var/run/xrouter"
	default:
		panic("unsupported OS")
	}
	if _, err := os.Stat(dirPath); err != nil {
		err = os.MkdirAll(dirPath, 0755)
		if err != nil {
			panic(err)
		}
	}
	return dirPath
}

// GetEtcDir /etc/xrouter，不存在时创建
func GetEtcDir() string {
	var dirPath string
	switch runtime.GOOS {
	case "windows":
		dirPath = "C:\\ProgramData\\xrouter"
	case "linux", "darwin":
		dirPath = "/etc/xrouter"
	default:
		panic("unsupported OS")
	}
	if _, err := os.Stat(dirPath); err != nil {
		err = os.MkdirAll(dirPath, 0755)
		if err != nil {
			panic(err)
		}
	}
	return dirPath
}
