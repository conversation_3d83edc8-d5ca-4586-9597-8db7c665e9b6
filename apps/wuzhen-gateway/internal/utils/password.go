package utils

import (
	"crypto/rand"
	"crypto/sha256"
	"fmt"
	"math/big"
)

const (
	upperLetters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
	lowerLetters = "abcdefghijklmnopqrstuvwxyz"
	digits       = "0123456789"
	specialChars = "!@#$%^&*()-_=+[]{}|;:,.<>?/`~"
	allChars     = upperLetters + lowerLetters + digits + specialChars
)

func randomCharFromSet(charSet string) byte {
	index, _ := rand.Int(rand.Reader, big.NewInt(int64(len(charSet))))
	return charSet[index.Int64()]
}

func GeneratePassword(length int) string {
	if length < 4 {
		return ""
	}

	password := make([]byte, length)

	charTypes := []string{upperLetters, lowerLetters, digits, specialChars}
	for i, charSet := range charTypes {
		char := randomCharFromSet(charSet)
		password[i] = char
	}

	for i := 4; i < length; i++ {
		char := randomCharFromSet(allChars)
		password[i] = char
	}

	for i := range password {
		j, _ := rand.Int(rand.Reader, big.NewInt(int64(len(password))))
		password[i], password[j.Int64()] = password[j.Int64()], password[i]
	}

	return string(password)
}

func ClientHashPassword(password string) string {
	return fmt.Sprintf("%x", sha256.Sum256([]byte(fmt.Sprintf("pass-%s", password))))
}

func HashPassword(password string) string {
	return fmt.Sprintf("%x", sha256.Sum256([]byte(fmt.Sprintf("!@#%s#@!", password))))
}
