package utils

import (
	"encoding/json"
	"fmt"
	"testing"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
)

var key = []byte("x3XFGAyGG27NfsaGcNmZ")

func TestJwt(t *testing.T) {
	claims := NewCustomClaims(uuid.New(), time.Hour)
	token, err := GenerateJwt(key, jwt.SigningMethodHS256, claims)
	if err != nil {
		t.Error(err)
		return
	}
	parse, err := ParseJwt(key, token)
	if err != nil {
		t.Error(err)
		return
	}
	content, err := json.<PERSON>(parse)
	if err != nil {
		t.<PERSON>rror(err)
		return
	}
	fmt.Printf("%s\n", content)
}
