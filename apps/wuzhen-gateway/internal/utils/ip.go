package utils

import (
	"fmt"
	"net"
	"strings"
)

func IncrementIP(ip net.IP) net.IP {
	// 将 IP 地址转为 32 位整数
	ip = ip.To4() // 确保是 IPv4
	if ip == nil {
		return nil // 如果不是 IPv4 地址，返回 nil
	}

	// 将 IP 地址转换为一个 uint32
	ipInt := uint32(ip[0])<<24 | uint32(ip[1])<<16 | uint32(ip[2])<<8 | uint32(ip[3])

	// 计算加 1 的结果
	ipInt++

	// 将结果转换回 net.IP
	return net.IPv4(byte(ipInt>>24), byte(ipInt>>16&0xFF), byte(ipInt>>8&0xFF), byte(ipInt&0xFF))
}
func DecrementIP(ip net.IP) net.IP {
	// 将 IP 地址转为 32 位整数
	ip = ip.To4() // 确保是 IPv4
	if ip == nil {
		return nil // 如果不是 IPv4 地址，返回 nil
	}

	// 将 IP 地址转换为一个 uint32
	ipInt := uint32(ip[0])<<24 | uint32(ip[1])<<16 | uint32(ip[2])<<8 | uint32(ip[3])

	// 计算减 1 的结果
	ipInt--

	// 将结果转换回 net.IP
	return net.IPv4(byte(ipInt>>24), byte(ipInt>>16&0xFF), byte(ipInt>>8&0xFF), byte(ipInt&0xFF))
}

// GenerateMAC 根据给定的 IP 地址生成一个 MAC 地址
func GenerateMAC(ip net.IP) net.HardwareAddr {
	// 如果 IP 地址是 IPv4
	if ip.To4() != nil {
		// 将 IPv4 地址转换为字节切片
		ipBytes := ip.To4()
		// 生成 MAC 地址，使用最后三字节作为 MAC 地址的后半部分
		mac := []byte{0x02, 0x00, ipBytes[0], ipBytes[1], ipBytes[2], ipBytes[3]} // 以 02 开头表示局域网地址
		return mac
	}
	return []byte{}
}

// RestoreIP 根据给定的 MAC 地址还原出相应的 IP 地址
func RestoreIP(mac net.HardwareAddr) (net.IP, error) {
	// 检查 MAC 地址的长度
	if len(mac) != 6 {
		return nil, fmt.Errorf("无效的 MAC 地址")
	}

	// 检查 MAC 地址的前缀是否为 02
	if mac[0] != 0x02 {
		return nil, fmt.Errorf("不支持的 MAC 地址前缀，必须以 02 开头")
	}
	// 还原 IPv4 地址
	ipv4 := net.IPv4(mac[2], mac[3], mac[4], mac[5])
	return ipv4, nil
}
func CodingIp(ipStr string) string {
	// 解析 IP 地址
	ip := net.ParseIP(ipStr)
	if ip == nil {
		return ipStr
	}
	// 将 IP 地址转换为字符串
	ipParts := strings.Split(ip.String(), ".")
	// 替换最后一位为 '*'
	if len(ipParts) == 4 {
		ipParts[3] = "*"
	}
	// 重新组合成 IP 地址字符串
	newIP := strings.Join(ipParts, ".")
	return newIP
}
