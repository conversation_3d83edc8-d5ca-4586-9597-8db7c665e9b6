package utils

import (
	"encoding/binary"
	"net"
)

type IPv4Header struct {
	Version       uint8
	IHL           uint8
	TOS           uint8
	TotalLen      uint16
	ID            uint16
	Flags         uint16
	TTL           uint8
	Protocol      uint8
	Checksum      uint16
	SourceIP      net.IP
	DestinationIP net.IP
}

type TCPHeader struct {
	SourcePort      uint16
	DestinationPort uint16
	SequenceNum     uint32
	AckNum          uint32
	DataOffset      uint8
	Flags           uint8
	Window          uint16
	Checksum        uint16
	UrgentPtr       uint16
}

func ParseIPv4Header(data []byte) *IPv4Header {
	if len(data) < 20 {
		return nil
	}
	header := &IPv4Header{}

	header.Version = data[0] >> 4
	header.IHL = data[0] & 0x0F
	header.TOS = data[1]
	header.TotalLen = binary.BigEndian.Uint16(data[2:4])
	header.ID = binary.BigEndian.Uint16(data[4:6])
	header.Flags = binary.BigEndian.Uint16(data[6:8])
	header.TTL = data[8]
	header.Protocol = data[9]
	header.Checksum = binary.BigEndian.Uint16(data[10:12])
	header.SourceIP = net.IPv4(data[12], data[13], data[14], data[15])
	header.DestinationIP = net.IPv4(data[16], data[17], data[18], data[19])

	return header
}

func ParseTCPHeader(data []byte) *TCPHeader {
	if len(data) < 20 {
		return nil
	}

	header := &TCPHeader{}

	header.SourcePort = binary.BigEndian.Uint16(data[0:2])
	header.DestinationPort = binary.BigEndian.Uint16(data[2:4])
	header.SequenceNum = binary.BigEndian.Uint32(data[4:8])
	header.AckNum = binary.BigEndian.Uint32(data[8:12])
	header.DataOffset = (data[12] >> 4) & 0x0F
	header.Flags = data[13]
	header.Window = binary.BigEndian.Uint16(data[14:16])
	header.Checksum = binary.BigEndian.Uint16(data[16:18])
	header.UrgentPtr = binary.BigEndian.Uint16(data[18:20])

	return header
}
