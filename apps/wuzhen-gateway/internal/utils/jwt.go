package utils

import (
	"errors"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
)

type CustomClaims struct {
	jwt.RegisteredClaims
	UserID uuid.UUID `json:"user_id"`
}

func NewCustomClaims(userId uuid.UUID, expires time.Duration) *CustomClaims {
	return &CustomClaims{
		UserID: userId,
		RegisteredClaims: jwt.RegisteredClaims{
			Issuer:    "xrouter",
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(expires)),
		},
	}
}

// GenerateJwt 生成JWT
func GenerateJwt(key any, method jwt.SigningMethod, claims jwt.Claims) (string, error) {
	token := jwt.NewWithClaims(method, claims)
	return token.SignedString(key)
}

// GenerateLongTermJwt 生成长期有效JWT
func GenerateLongTermJwt(key any, method jwt.SigningMethod, userId uuid.UUID) (string, error) {
	token := jwt.NewWithClaims(
		method,
		&CustomClaims{
			UserID: userId,
			RegisteredClaims: jwt.RegisteredClaims{
				Issuer:   "xrouter-long-term",
				IssuedAt: jwt.NewNumericDate(time.Now()),
			},
		})
	return token.SignedString(key)
}

// ParseJwt 解析JWT
func ParseJwt(key any, jwtStr string, options ...jwt.ParserOption) (*CustomClaims, error) {
	var c CustomClaims
	token, err := jwt.ParseWithClaims(jwtStr, &c, func(token *jwt.Token) (interface{}, error) {
		return key, nil
	}, options...)
	if err != nil {
		return nil, err
	}
	// 校验 Claims 对象是否有效，基于 exp（过期时间），nbf（不早于），iat（签发时间）等进行判断（如果有这些声明的话）。
	if !token.Valid {
		return nil, errors.New("invalid token")
	}
	return &c, nil
}
