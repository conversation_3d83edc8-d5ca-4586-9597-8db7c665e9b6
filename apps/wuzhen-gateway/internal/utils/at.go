package utils

import (
	"encoding/binary"
	"encoding/hex"
	"sync"
	"time"

	"github.com/tarm/serial"
)

var atLock = sync.Mutex{}

type AtSerial struct {
	*serial.Port
}

func OpenSerial(path string, baud int) (*AtSerial, error) {
	atLock.Lock()
	a, err := serial.OpenPort(&serial.Config{
		Name: path, // 根据您的设备设置串口名称
		Baud: baud, // 波特率，根据设备要求进行调整
	})
	return &AtSerial{a}, err
}
func (a AtSerial) Close() error {
	atLock.Unlock()
	return a.Port.Close()
}
func (a AtSerial) SendATCommand(command string) (string, error) {
	_, err := a.Write([]byte(command + "\r")) // 添加换行符
	if err != nil {
		return "", err
	}
	// 等待设备处理命令
	time.Sleep(1 * time.Second)
	return a.ReadResponse()
}
func (a AtSerial) ReadResponse() (string, error) {
	bytes := make([]byte, 1024*1024)
	n, err := a.Read(bytes)
	if err != nil {
		return "", err
	}
	return string(bytes[:n]), nil
}
func (AtSerial) ToStr(hexStr string) string {
	// 将十六进制字符串解码为字节数组
	data, err := hex.DecodeString(hexStr)
	if err != nil {
		return ""
	}
	// 创建一个切片来存放转换后的字符
	var runes []rune
	// 以 UTF-16 编码读取数据
	for i := 0; i < len(data); i += 2 {
		// 确保不会越界
		if i+1 >= len(data) {
			break
		}
		// 将两个字节转换为一个 rune
		runeValue := binary.BigEndian.Uint16(data[i : i+2])
		runes = append(runes, rune(runeValue))
	}
	// 将 runes 转换为字符串
	return string(runes)
}
