package utils

import (
	"crypto/rand"
	"crypto/rsa"
	"crypto/x509"
	"crypto/x509/pkix"
	"encoding/pem"
	"math/big"
	"os"
	"time"
)

func Cert() {
	// 生成 RSA 私钥
	priv, err := rsa.GenerateKey(rand.Reader, 2048)
	if err != nil {
		panic(err)
	}
	// 创建自签名证书模板
	certTemplate := x509.Certificate{
		SerialNumber: big.NewInt(1),
		Subject: pkix.Name{
			Organization: []string{"XRouter"},
		},
		NotBefore: time.Now(),
		NotAfter:  time.Now().Add(99 * 365 * 24 * time.Hour), // 有效期为 99 年

		KeyUsage:    x509.KeyUsageKeyEncipherment | x509.KeyUsageDigitalSignature,
		ExtKeyUsage: []x509.ExtKeyUsage{x509.ExtKeyUsageServerAuth},
	}
	// 生成自签名证书
	certDER, err := x509.CreateCertificate(
		rand.Reader,
		&certTemplate,
		&certTemplate,
		&priv.<PERSON><PERSON>ey,
		priv,
	)
	if err != nil {
		panic(err)
	}

	// 将私钥保存到文件
	privFile, err := os.Create("server.key")
	if err != nil {
		panic(err)
	}
	defer privFile.Close()

	if err := pem.Encode(privFile, &pem.Block{Type: "PRIVATE KEY", Bytes: x509.MarshalPKCS1PrivateKey(priv)}); err != nil {
		panic(err)
	}

	// 将证书保存到文件
	certFile, err := os.Create("server.crt")
	if err != nil {
		panic(err)
	}
	defer certFile.Close()

	if err := pem.Encode(certFile, &pem.Block{Type: "CERTIFICATE", Bytes: certDER}); err != nil {
		panic(err)
	}

	println("自签名证书和私钥已生成：server.crt 和 server.key")
}
