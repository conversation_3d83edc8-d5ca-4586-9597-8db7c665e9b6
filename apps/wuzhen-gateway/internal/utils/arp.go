package utils

import (
	"bufio"
	"net"
	"net/netip"
	"os"
	"strings"
	"time"

	"github.com/mdlayher/arp"
)

func Arp(Interface, ip string) (net.HardwareAddr, error) {
	iface, err := net.InterfaceByName(Interface)
	if err != nil {
		return nil, err
	}
	client, err := arp.Dial(iface)
	if err != nil {
		return nil, err
	}
	addr, err := netip.ParseAddr(ip)
	if err != nil {
		return nil, err
	}
	client.SetDeadline(time.Now().Add(time.Second))
	return client.Resolve(addr)
}

// ARPEntry 代表一个ARP记录条目
type ARPEntry struct {
	IPAddress string
	HWAddress string
	Flags     string
	Mask      string
	Device    string
}

// GetARPTable 从 /proc/net/arp 获取ARP表
func GetARPTable() ([]ARPEntry, error) {
	file, err := os.Open("/proc/net/arp")
	if err != nil {
		return nil, err
	}
	defer file.Close()
	var arpEntries []ARPEntry
	scanner := bufio.NewScanner(file)
	// 跳过文件的第一行（标题行）
	scanner.Scan()
	for scanner.Scan() {
		line := scanner.Text()
		parts := strings.Fields(line)
		if len(parts) >= 5 {
			entry := ARPEntry{
				IPAddress: parts[0],
				HWAddress: parts[3],
				Flags:     parts[2],
				Mask:      parts[4],
				Device:    parts[5],
			}
			arpEntries = append(arpEntries, entry)
		}
	}
	if err = scanner.Err(); err != nil {
		return nil, err
	}
	return arpEntries, nil
}
