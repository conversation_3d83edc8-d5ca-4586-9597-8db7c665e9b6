package utils

import (
	"fmt"
	"net"

	"github.com/vishvananda/netlink"
)

// GetBridgeInterfaces 获取网桥下的接口列表
func GetBridgeInterfaces(br string) []NetLink {
	resList := make([]NetLink, 0)
	bridge, err := netlink.LinkByName(br)
	if err != nil {
		return resList
	}
	// 获取网桥下的所有下属接口
	links, err := netlink.LinkList()
	if err != nil {
		return resList
	}
	for _, link := range links {
		if link.Attrs().MasterIndex == bridge.Attrs().Index {
			resList = append(resList, NetLink{
				Name:         link.Attrs().Name,
				OperState:    link.Attrs().OperState.String(),
				HardwareAddr: link.Attrs().HardwareAddr.String(),
			})
		}
	}
	return resList
}
func GetInterfaceGateway(interfaceName string) string {
	// 获取所有路由
	routes, err := netlink.RouteList(nil, netlink.FAMILY_V4)
	if err != nil {
		return ""
	}
	// 遍历路由，查找 eth0 的网关地址
	var gateway net.IP
	for _, route := range routes {
		if route.LinkIndex == getInterfaceIndex(interfaceName) {
			gateway = route.Gw
			break
		}
	}
	if gateway != nil {
		return gateway.String()
	} else {
		return ""
	}
}

// 获取指定网络接口的索引
func getInterfaceIndex(ifaceName string) int {
	iface, err := netlink.LinkByName(ifaceName)
	if err != nil {
		return 0
	}
	return iface.Attrs().Index
}
func AddIPRule(ip string, tableID int) error {
	newRule := netlink.NewRule()
	newRule.Family = netlink.FAMILY_V4
	newRule.Src = &net.IPNet{IP: net.ParseIP(ip), Mask: net.CIDRMask(32, 32)}
	newRule.Table = tableID
	if err := netlink.RuleAdd(newRule); err != nil {
		return fmt.Errorf("failed to add rule: %v", err)
	}
	return nil
}

func DeleteIPRule(ip string, tableID int) error {
	list, err := netlink.RuleList(netlink.FAMILY_V4)
	if err != nil {
		return fmt.Errorf("failed to list rules: %v", err)
	}
	for _, r := range list {
		if r.Src != nil {
			if r.Src.IP.String() == ip && r.Table == tableID {
				if err = netlink.RuleDel(&r); err != nil {
					return fmt.Errorf("failed to delete rule: %v", err)
				}
			}
		}
	}
	return nil
}

func DeleteIPRuleFormTableID(tableID int) error {
	list, err := netlink.RuleList(netlink.FAMILY_V4)
	if err != nil {
		return fmt.Errorf("failed to list rules: %v", err)
	}
	for _, r := range list {
		if r.Src != nil {
			if r.Table == tableID {
				if err = netlink.RuleDel(&r); err != nil {
					return fmt.Errorf("failed to delete rule: %v", err)
				}
			}
		}
	}
	return nil
}

func ruleExists(rules []netlink.Rule, rule netlink.Rule) bool {
	for _, r := range rules {
		if r.Table == rule.Table && r.Src.String() == rule.Src.String() {
			return true
		}
	}
	return false
}
