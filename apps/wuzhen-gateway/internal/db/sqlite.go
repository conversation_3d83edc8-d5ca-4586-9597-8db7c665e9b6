package db

import (
	"fmt"
	"gateway/internal/config"
	"gateway/internal/log"
	"gateway/internal/model"
	"gateway/internal/utils"
	"time"

	gosqlite "github.com/glebarez/sqlite"
	"github.com/google/uuid"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"gorm.io/gorm/schema"
)

var db *gorm.DB

func InitDB() error {
	var err error
	db, err = sqliteClient()
	if err != nil {
		return err
	}
	err = db.AutoMigrate(
		[]interface{}{
			&model.Proxies{},
			&model.IpRule{},
			&model.Path{},
			&model.User{},
			&model.LoginLog{},
			model.Traffic{},
			model.Client{},
			model.Config{},
			model.Storage{},
			model.CacheLog{},
		}...)
	if err != nil {
		log.GetLogger().Errorln(fmt.Sprintf("AutoMigrate error: %s", err.<PERSON>rror()))
	}
	pass := "admin@123"
	admin := model.User{
		ID:       uuid.New(),
		Account:  "admin",
		Password: utils.HashPassword(utils.ClientHashPassword(pass)),
		IsAdmin:  true,
		IsActive: true,
	}
	user := model.User{}
	result := db.Where(model.User{IsAdmin: true}).Attrs(admin).FirstOrCreate(&user)
	return result.Error
}

type DB struct {
	Db *gorm.DB
}

func GetDb() *gorm.DB {
	return db
}
func Close() error {
	sqlDB, err := db.DB()
	if err != nil {
		return err
	}
	return sqlDB.Close()
}

func sqliteClient() (*gorm.DB, error) {
	filePath := "xrouter.db"
	_logger := logger.Default

	gormLevel := logger.Error
	if config.Config().Dev {
		gormLevel = logger.Info
	}

	_logger = logger.New(log.GetLogger(), logger.Config{
		SlowThreshold:             200 * time.Millisecond,
		LogLevel:                  gormLevel,
		IgnoreRecordNotFoundError: true,
		Colorful:                  false,
	})
	dbClient, err := gorm.Open(gosqlite.Open(filePath), &gorm.Config{
		PrepareStmt:                              true,
		Logger:                                   _logger,
		DisableForeignKeyConstraintWhenMigrating: true,
		NamingStrategy:                           schema.NamingStrategy{SingularTable: true},
	})
	if err != nil {
		return nil, err
	}
	return dbClient, nil
}
