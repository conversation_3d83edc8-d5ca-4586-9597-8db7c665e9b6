package _interface

import (
	"gateway/internal/model"
	"net/netip"

	"github.com/google/uuid"
	C "github.com/metacubex/mihomo/constant"
)

var ClashApi _ClashApi

type _ClashApi interface {
	Delay(name string) int
	GetProxies() map[string]C.Proxy
	CloseSourceIpConnection(sourceIp string)
	GetAutoGuardNow() string
	GetGetAutoNow() string
	GetAutoPassGFWNow() (string, bool)
	GetAutoPath() string
	NewInitPath(ip string, userid uuid.UUID) *model.Path
	GetConnectCount() map[netip.Addr]int
}
