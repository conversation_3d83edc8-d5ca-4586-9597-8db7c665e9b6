package _interface

import (
	"sync"
	"time"
)

var StatusApi _StatusApi

type _StatusApi interface {
	GetNetUptime() time.Duration
	GetCpu() uint64
	GetMem() uint64
	GetActiveIpMap() *sync.Map
	GetLoad() bool
	SetLoad(b bool)
	GetPassGfw() bool
	GetWanDelay() int64
	GetWanDns() bool
	GetWgIPRuleTableID() uint64
	SetWgIPRuleTableID(uint64)
	GetTunDelay() int64
	SetEsimICCid(iccid string)
	GetEsimICCid() string
	GetTotaleSIMTraffic() uint64
	GetUseEsimTraffic() uint64
	RestUseEsimTraffic()
}
