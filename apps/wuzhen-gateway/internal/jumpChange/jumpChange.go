package jumpChange

import (
	"encoding/json"
	"gateway/internal/clash"
	"gateway/internal/db"
	"gateway/internal/log"
	"gateway/internal/model"
	"time"

	"golang.org/x/exp/slices"
)

func Init() {
	go func() {
		ticker := time.NewTicker(time.Minute)
		for t := range ticker.C {
			go do(t)
		}
	}()
}
func do(t time.Time) {
	model.ProxiesLock.Lock()
	defer model.ProxiesLock.Unlock()
	getDb := db.GetDb()
	paths := make([]model.Path, 0)
	proxies := make([]model.Proxies, 0)
	getDb.Model(&model.Proxies{}).
		Where("use_status IN ? AND delay > 0", []string{model.ProxyUseStatusUse, model.ProxyUseStatusNone}).
		Find(&proxies)
	getDb.Model(&model.Path{}).
		Where("change_time > 0 AND name IN (SELECT proxy FROM ip_rule)").
		Find(&paths)
	pMap := make(map[string]map[string]struct{})
	gMap := make(map[string]map[string]struct{}) //守卫
	eMap := make(map[string]map[string]struct{}) //出口
	for _, proxy := range proxies {
		if proxy.Type == model.ProxyTypeGuardProxy {
			if gMap[proxy.CountryCode] == nil {
				gMap[proxy.CountryCode] = make(map[string]struct{})
			}
			gMap[proxy.CountryCode][proxy.Name] = struct{}{}
		}
		if proxy.Type == model.ProxyTypeExitProxy {
			if eMap[proxy.CountryCode] == nil {
				eMap[proxy.CountryCode] = make(map[string]struct{})
			}
			eMap[proxy.CountryCode][proxy.Name] = struct{}{}
		}
		if proxy.Type == model.ProxyTypePassGfw {
			if pMap[proxy.CountryCode] == nil {
				pMap[proxy.CountryCode] = make(map[string]struct{})
			}
			pMap[proxy.CountryCode][proxy.Name] = struct{}{}
		}
	}
	count := 0
	for _, path := range paths {
		exp := time.Unix(path.UpdatedAt, 0).Add(time.Duration(path.ChangeTime) * time.Second)
		if t.After(exp) && len(path.Proxies) == len(path.ChangeCountryArray) {
			//改变
			for i, codeList := range path.ChangeCountryArray {
				codeMap := make(map[string]struct{})

				if slices.Contains(codeList, "all") {
					// 包含选择了全部国家
					if i == len(path.ChangeCountryArray)-1 {
						// 将所有出口节点国家加入map
						for code := range eMap {
							codeMap[code] = struct{}{}
						}
					} else if i == 0 {
						for code := range pMap {
							codeMap[code] = struct{}{}
						}
					} else {
						// 将所有守护节点国家加入map
						for code := range gMap {
							codeMap[code] = struct{}{}
						}
					}
				} else {
					// 指定国家范围
					for _, s := range codeList {
						codeMap[s] = struct{}{}
					}
				}

				if i == len(path.ChangeCountryArray)-1 { //出口
					for code := range codeMap {
						for k := range eMap[code] {
							if k != path.Proxies[i] {
								path.Proxies[i] = k
								break
							}
						}
					}
				} else if i == 0 {
					for code := range codeMap {
						for k := range pMap[code] {
							if k != path.Proxies[i] {
								path.Proxies[i] = k
								break
							}
						}
					}
				} else {
					for code := range codeMap {
						for k := range gMap[code] {
							if k != path.Proxies[i] {
								path.Proxies[i] = k
								break
							}
						}
					}
				}
			}
			marshal, _ := json.Marshal(path.Proxies)
			getDb.Model(&model.Path{}).
				Where("name = ?", path.Name).
				Update("proxies", string(marshal))
			count++
		}
	}
	if count > 0 {
		err := clash.HotUpdate()
		if err != nil {
			log.GetLogger().Errorln("clash hot update error", err.Error())
		}
	}
}
