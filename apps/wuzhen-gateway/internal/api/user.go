package api

import (
	"encoding/json"
	"errors"
	"gateway/internal/cache"
	"gateway/internal/config"
	"gateway/internal/db"
	"gateway/internal/model"
	"gateway/internal/utils"
	"net/http"
	"regexp"
	"strings"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"github.com/gorilla/mux"
	"gorm.io/gorm"
)

type loginReq struct {
	Account  string `json:"account"`
	Password string `json:"password"`
}

type loginResp struct {
	Token      string    `json:"token"`
	ExpireTime time.Time `json:"expire_time"`
}

func login(w http.ResponseWriter, r *http.Request) {
	switch r.Method {
	case http.MethodPost:
		var req loginReq
		err := json.NewDecoder(r.Body).Decode(&req)
		if err != nil {
			Response(w, r, http.StatusBadRequest, err.Error(), nil)
			return
		}

		// 检查是否超过登录限制
		if _, exists := cache.GetCache().Get(req.Account); exists {
			// 从内存获取到已超过登录限制
			Response(w, r, http.StatusBadRequest, "out of login limit.", nil)
			return
		}
		var loginUser model.User
		result := db.GetDb().
			Where("account = ? AND password = ?", req.Account, utils.HashPassword(req.Password)).
			Take(&loginUser)
		if result.Error != nil && !errors.Is(result.Error, gorm.ErrRecordNotFound) {
			Response(w, r, http.StatusInternalServerError, result.Error.Error(), nil)
			return
		}
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			// 获取10分钟内已登录失败次数
			var count int64
			count, err = model.GetAccountLoginFailCount(db.GetDb(), req.Account, time.Minute*10)
			if err != nil {
				Response(w, r, http.StatusInternalServerError, err.Error(), nil)
				return
			}
			// 记录登录失败日志
			db.GetDb().
				Create(model.NewLoginLog(model.LoginStateFail, getClientIP(r), req.Account, nil))
			if count+1 >= 5 {
				// 如果当前是最后一次登录失败, 添加锁定信息到内存
				cache.GetCache().Set(req.Account, struct{}{}, time.Now().Add(10*time.Minute))
				Response(w, r, http.StatusBadRequest, "out of login limit.", nil)
				return
			}
			Response(w, r, http.StatusBadRequest, "invalid account or password.", 5-(count+1))
			return
		}
		// 登录成功
		claims := utils.NewCustomClaims(loginUser.ID, 86400*time.Second)
		token, err := utils.GenerateJwt(
			[]byte(config.Config().JwtSecret),
			jwt.SigningMethodHS256,
			claims,
		)
		if err != nil {
			Response(w, r, http.StatusInternalServerError, err.Error(), nil)
			return
		}
		// 记录登录成功记录
		db.GetDb().
			Create(model.NewLoginLog(model.LoginStateSuccess, getClientIP(r), req.Account, &loginUser.ID))
		var resp = loginResp{
			Token:      token,
			ExpireTime: claims.ExpiresAt.Time,
		}
		Response(w, r, http.StatusOK, ResponseMsgSuccess, &resp)
	default:
		Response(w, r, http.StatusBadRequest, "method not supported", nil)
	}
}

type userInfoResp struct {
	ID          uuid.UUID `json:"id"`
	Account     string    `json:"account"`
	IsAdmin     bool      `json:"is_admin"`
	IsActive    bool      `json:"is_active"`
	CreatedAt   time.Time `json:"created_at"`
	LastLoginAt time.Time `json:"last_login_at"`
}

func userInfo(w http.ResponseWriter, r *http.Request) {
	switch r.Method {
	case http.MethodGet:
		u, ok := r.Context().Value("user").(model.User)
		if !ok {
			Response(w, r, http.StatusForbidden, "no permissions", nil)
			return
		}
		log, err := model.GetLastLoginLog(db.GetDb(), u.ID)
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			Response(w, r, http.StatusInternalServerError, err.Error(), nil)
			return
		}
		resp := userInfoResp{
			ID:          u.ID,
			Account:     u.Account,
			IsAdmin:     u.IsAdmin,
			IsActive:    u.IsActive,
			CreatedAt:   time.Unix(u.CreatedAt, 0),
			LastLoginAt: time.Unix(log.CreatedAt, 0),
		}
		Response(w, r, http.StatusOK, ResponseMsgSuccess, &resp)
	default:
		Response(w, r, http.StatusBadRequest, "method not supported", nil)
	}
}

func userList(w http.ResponseWriter, r *http.Request) {
	switch r.Method {
	case http.MethodGet:
		list := make([]model.User, 0)
		err := db.GetDb().Model(&model.User{}).Order("created_at desc").Find(&list).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			Response(w, r, http.StatusInternalServerError, err.Error(), nil)
			return
		}
		var resp = make([]userInfoResp, len(list))
		for i, u := range list {
			log, err := model.GetLastLoginLog(db.GetDb(), u.ID)
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				Response(w, r, http.StatusInternalServerError, err.Error(), nil)
				return
			}
			var loginAt int64
			if log != nil {
				loginAt = log.CreatedAt
			}
			resp[i] = userInfoResp{
				ID:          u.ID,
				Account:     u.Account,
				IsAdmin:     u.IsAdmin,
				IsActive:    u.IsActive,
				CreatedAt:   time.Unix(u.CreatedAt, 0),
				LastLoginAt: time.Unix(loginAt, 0),
			}
		}
		Response(w, r, http.StatusOK, ResponseMsgSuccess, &resp)
	default:
		Response(w, r, http.StatusBadRequest, "method not supported", nil)
	}
}

func getClientIP(r *http.Request) string {
	// 尝试从X-Forwarded-For头中获取IP地址
	xff := r.Header.Get("X-Forwarded-For")
	if xff != "" {
		ips := strings.Split(xff, ",")
		return strings.TrimSpace(ips[0])
	}

	// 尝试从X-Real-IP头中获取IP地址
	xri := r.Header.Get("X-Real-IP")
	if xri != "" {
		return xri
	}

	// 从RemoteAddr中获取IP地址
	ip := r.RemoteAddr
	if strings.Contains(ip, "::1") {
		return "127.0.0.1"
	} else {
		if strings.Contains(ip, ":") {
			ip = strings.Split(ip, ":")[0]
		}
	}
	return ip
}

type addUserReq struct {
	Account  string `json:"account"`
	Password string `json:"password"`
}
type addUserResp struct {
	ID uuid.UUID `json:"id"`
}

func user(w http.ResponseWriter, r *http.Request) {
	switch r.Method {
	case http.MethodPost:
		// 新增用户
		var req addUserReq
		err := json.NewDecoder(r.Body).Decode(&req)
		if err != nil {
			Response(w, r, http.StatusBadRequest, err.Error(), nil)
			return
		}
		// 参数校验 账号需不小于4个字符
		if len(req.Account) < 4 || len(req.Password) < 8 {
			Response(w, r, http.StatusBadRequest, "invalid account.", nil)
			return
		}
		// 校验账号是否存在
		var namesakeCount int64
		err = db.GetDb().
			Model(&model.User{}).
			Where("account = ?", req.Account).
			Count(&namesakeCount).
			Error
		if err != nil {
			Response(w, r, http.StatusInternalServerError, err.Error(), nil)
			return
		}
		if namesakeCount > 0 {
			Response(w, r, http.StatusBadRequest, "account already exists.", nil)
			return
		}
		var u = model.User{
			ID:       uuid.New(),
			Account:  req.Account,
			Password: utils.HashPassword(req.Password),
		}
		err = db.GetDb().Create(&u).Error
		if err != nil {
			Response(w, r, http.StatusInternalServerError, err.Error(), nil)
			return
		}
		var resp = addUserResp{
			ID: u.ID,
		}
		Response(w, r, http.StatusOK, ResponseMsgSuccess, &resp)
	case http.MethodDelete:
		// 删除用户
		userIdList := make([]uuid.UUID, 0)
		vars := mux.Vars(r)
		if idStr, exists := vars["id"]; exists {
			// 验证uuid格式
			id, err := uuid.Parse(idStr)
			if err != nil {
				Response(w, r, http.StatusBadRequest, "invalid uuid format.", nil)
				return
			}
			userIdList = append(userIdList, id)
		} else {
			// 批量删除
			var req []string
			err := json.NewDecoder(r.Body).Decode(&req)
			if err != nil {
				Response(w, r, http.StatusBadRequest, err.Error(), nil)
				return
			}
			for _, str := range req {
				// 验证uuid格式
				id, err := uuid.Parse(str)
				if err != nil {
					Response(w, r, http.StatusBadRequest, "invalid uuid format.", nil)
					return
				}
				userIdList = append(userIdList, id)
			}
		}
		resp := make([]addUserResp, 0)
		for _, userId := range userIdList {
			var u model.User
			err := db.GetDb().First(&u, userId).Error
			if err != nil {
				Response(w, r, http.StatusInternalServerError, err.Error(), nil)
				return
			}
			if u.IsAdmin {
				// 不能删除管理员账号
				Response(w, r, http.StatusForbidden, "no permissions", nil)
				return
			}
			err = db.GetDb().Delete(&u).Error
			if err != nil {
				Response(w, r, http.StatusInternalServerError, err.Error(), nil)
				return
			}
			resp = append(resp, addUserResp{ID: userId})
		}

		Response(w, r, http.StatusOK, ResponseMsgSuccess, &resp)
	default:
		Response(w, r, http.StatusBadRequest, "method not supported", nil)
	}
}

type changePasswordReq struct {
	Password string `json:"password"`
}
type changePasswordResp struct {
	ID uuid.UUID `json:"id"`
}

func userPassword(w http.ResponseWriter, r *http.Request) {
	switch r.Method {
	case http.MethodPatch:
		var req changePasswordReq
		err := json.NewDecoder(r.Body).Decode(&req)
		if err != nil {
			Response(w, r, http.StatusBadRequest, err.Error(), nil)
			return
		}
		// 更新用户密码
		vars := mux.Vars(r)
		if idStr, exists := vars["id"]; exists {
			// 更新指定用户密码
			// 验证uuid格式
			id, err := uuid.Parse(idStr)
			if err != nil {
				Response(w, r, http.StatusBadRequest, "invalid uuid format.", nil)
				return
			}
			// 查询用户
			var u model.User
			err = db.GetDb().First(&u, id).Error
			if err != nil {
				Response(w, r, http.StatusInternalServerError, err.Error(), nil)
				return
			}

			err = db.GetDb().Model(&u).Update("password", utils.HashPassword(req.Password)).Error
			if err != nil {
				Response(w, r, http.StatusInternalServerError, err.Error(), nil)
				return
			}

			// 更新成功
			var resp = changePasswordResp{
				ID: u.ID,
			}
			Response(w, r, http.StatusOK, ResponseMsgSuccess, &resp)
		} else {
			// 更新自己密码
			u, ok := r.Context().Value("user").(model.User)
			if !ok {
				Response(w, r, http.StatusForbidden, "no permissions", nil)
				return
			}
			if !u.IsActive {
				// 未激活, 修改密码自动激活
				err = db.GetDb().Model(&u).Updates(model.User{IsActive: true, Password: utils.HashPassword(req.Password)}).Error
				if err != nil {
					Response(w, r, http.StatusInternalServerError, err.Error(), nil)
					return
				}
			} else {
				err = db.GetDb().Model(&u).Update("password", utils.HashPassword(req.Password)).Error
				if err != nil {
					Response(w, r, http.StatusInternalServerError, err.Error(), nil)
					return
				}
			}
			// 更新成功
			var resp = changePasswordResp{
				ID: u.ID,
			}
			Response(w, r, http.StatusOK, ResponseMsgSuccess, &resp)
		}
	default:
		Response(w, r, http.StatusBadRequest, "method not supported", nil)
	}
}

func register(w http.ResponseWriter, r *http.Request) {
	switch r.Method {
	case http.MethodPost:
		// 新增用户
		var req addUserReq
		err := json.NewDecoder(r.Body).Decode(&req)
		if err != nil {
			Response(w, r, http.StatusBadRequest, err.Error(), nil)
			return
		}
		// 参数校验 账号需不小于4个字符, 并且只能有英文和数字组成
		re := regexp.MustCompile(`^[a-zA-Z0-9]+$`)
		if len(req.Account) < 4 || !re.MatchString(req.Account) || len(req.Password) < 8 {
			Response(w, r, http.StatusBadRequest, "invalid account.", nil)
			return
		}
		// 校验账号是否存在
		var namesakeCount int64
		err = db.GetDb().
			Model(&model.User{}).
			Where("account = ?", req.Account).
			Count(&namesakeCount).
			Error
		if err != nil {
			Response(w, r, http.StatusInternalServerError, err.Error(), nil)
			return
		}
		if namesakeCount > 0 {
			Response(w, r, http.StatusBadRequest, "account already exists.", nil)
			return
		}
		var u = model.User{
			ID:       uuid.New(),
			Account:  req.Account,
			Password: utils.HashPassword(req.Password),
			IsActive: true,
		}
		err = db.GetDb().Create(&u).Error
		if err != nil {
			Response(w, r, http.StatusInternalServerError, err.Error(), nil)
			return
		}
		var resp = addUserResp{
			ID: u.ID,
		}
		Response(w, r, http.StatusOK, ResponseMsgSuccess, &resp)
	default:
		Response(w, r, http.StatusBadRequest, "method not supported", nil)
	}
}
