package api

import (
	"encoding/json"
	"fmt"
	"gateway/internal/clash"
	"gateway/internal/constant"
	"gateway/internal/db"
	"gateway/internal/log"
	"gateway/internal/model"
	"gateway/internal/utils"
	"net/http"
	"os"
	"os/exec"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"
)

type wifiConfigurationReq struct {
	SSID     string `json:"ssid"`
	Password string `json:"password"`
	Switch   string `json:"switch"` //down up
}

var wifiLock sync.Mutex

func upstream(w http.ResponseWriter, r *http.Request) {
	wifiLock.Lock()
	defer wifiLock.Unlock()
	switch r.Method {
	case http.MethodPost:
		var req wifiConfigurationReq
		err := json.NewDecoder(r.Body).Decode(&req)
		if err != nil {
			Response(w, r, http.StatusBadRequest, err.Error(), nil)
			return
		}

		if req.Switch == "down" {
			err = exec.Command("systemctl", "stop", "wpa_supplicant").Run()
			if err != nil {
				Response(w, r, http.StatusInternalServerError, err.Error(), nil)
				log.GetLogger().Errorln("restart wpa error: ", err)
				return
			}
			err = model.SetConfig(db.GetDb(), model.UpstreamSwitch, req.Switch)
			if err != nil {
				Response(w, r, http.StatusInternalServerError, err.Error(), nil)
				return
			}
			Response(w, r, http.StatusOK, ResponseMsgSuccess, nil)
			return
		}
		if len(req.Password) < 8 {
			Response(w, r, http.StatusBadRequest, "invalid password.", nil)
			return
		}
		if len(req.SSID) == 0 {
			Response(w, r, http.StatusBadRequest, "invalid SSID.", nil)
			return
		}
		// 先保存到数据库
		err = model.SetConfig(db.GetDb(), model.UpstreamSSID, req.SSID)
		if err != nil {
			Response(w, r, http.StatusInternalServerError, err.Error(), nil)
			return
		}
		err = model.SetConfig(db.GetDb(), model.UpstreamPassword, req.Password)
		if err != nil {
			Response(w, r, http.StatusInternalServerError, err.Error(), nil)
			return
		}
		// 将上游WIFI信息写入文件
		err = os.WriteFile(
			"/etc/wpa_supplicant/wpa_supplicant.conf",
			[]byte(
				fmt.Sprintf(constant.StaTemplate, req.SSID, req.Password, req.SSID, req.Password),
			),
			0640,
		)
		if err != nil {
			Response(w, r, http.StatusInternalServerError, err.Error(), nil)
			log.GetLogger().Errorln("update wpa config error: ", err)
			return
		}
		err = exec.Command("ip", "addr", "flush", "dev", "wlan0").Run()
		if err != nil {
			Response(w, r, http.StatusInternalServerError, err.Error(), nil)
			log.GetLogger().Errorln("restart wpa error: ", err)
			return
		}
		if req.Switch == "up" {
			err = exec.Command("systemctl", "restart", "wpa_supplicant").Run()
			if err != nil {
				Response(w, r, http.StatusInternalServerError, err.Error(), nil)
				log.GetLogger().Errorln("restart wpa error: ", err)
				return
			}
			err = clash.HotUpdate()
			if err != nil {
				log.GetLogger().Errorf("hot update error: %s", err.Error())
				Response(w, r, http.StatusInternalServerError, "hot update error", nil)
				return
			}
			for i := 0; i < 30; i++ {
				ip := utils.GetInterfaceIP("wlan0")
				if ip != "" {
					err = model.SetConfig(db.GetDb(), model.UpstreamSwitch, req.Switch)
					if err != nil {
						Response(w, r, http.StatusInternalServerError, err.Error(), nil)
						return
					}
					Response(w, r, http.StatusOK, ResponseMsgSuccess, nil)
					return
				} else {
					output, _ := exec.Command("journalctl", "-u", "wpa_supplicant.service", "-b", "-n5", "--no-pager").Output()
					if strings.Contains(string(output), "auth_failures") {
						err = model.SetConfig(db.GetDb(), model.UpstreamSwitch, req.Switch)
						if err != nil {
							Response(w, r, http.StatusInternalServerError, err.Error(), nil)
							return
						}
						Response(w, r, http.StatusOK, "auth_failures", nil)
						return
					}
					time.Sleep(time.Second)
				}
			}
			Response(w, r, http.StatusBadRequest, "connection error", nil)
		}
		break
	case http.MethodGet:
		var (
			ssid     string
			password string
			_switch  string
		)
		ssid, _ = model.GetConfig(db.GetDb(), model.UpstreamSSID)
		password, _ = model.GetConfig(db.GetDb(), model.UpstreamPassword)
		_switch, _ = model.GetConfig(db.GetDb(), model.UpstreamSwitch)
		resp := wifiConfigurationReq{
			SSID:     ssid,
			Password: password,
			Switch:   _switch,
		}
		Response(w, r, http.StatusOK, ResponseMsgSuccess, resp)
		break
	default:
		Response(w, r, http.StatusBadRequest, "method not supported", nil)
	}
}

func downstream(w http.ResponseWriter, r *http.Request) {
	wifiLock.Lock()
	defer wifiLock.Unlock()
	switch r.Method {
	case http.MethodGet:
		var (
			ssid     string
			password string
			_switch  string
		)
		ssid, _ = model.GetConfig(db.GetDb(), model.DownstreamSSID)
		password, _ = model.GetConfig(db.GetDb(), model.DownstreamPassword)
		_switch, _ = model.GetConfig(db.GetDb(), model.DownstreamSwitch)
		resp := wifiConfigurationReq{
			SSID:     ssid,
			Password: password,
			Switch:   _switch,
		}
		Response(w, r, http.StatusOK, ResponseMsgSuccess, resp)
		break
	case http.MethodPost:
		var req wifiConfigurationReq
		err := json.NewDecoder(r.Body).Decode(&req)
		if err != nil {
			Response(w, r, http.StatusBadRequest, err.Error(), nil)
			return
		}
		if len(req.Password) < 8 {
			Response(w, r, http.StatusBadRequest, "invalid password.", nil)
			return
		}
		if len(req.SSID) == 0 {
			Response(w, r, http.StatusBadRequest, "invalid SSID.", nil)
			return
		}
		err = model.SetConfig(db.GetDb(), model.DownstreamSSID, req.SSID)
		if err != nil {
			Response(w, r, http.StatusInternalServerError, err.Error(), nil)
			return
		}
		err = model.SetConfig(db.GetDb(), model.DownstreamPassword, req.Password)
		if err != nil {
			Response(w, r, http.StatusInternalServerError, err.Error(), nil)
			return
		}
		err = model.SetConfig(db.GetDb(), model.DownstreamSwitch, req.Switch)
		if err != nil {
			Response(w, r, http.StatusInternalServerError, err.Error(), nil)
			return
		}
		err = os.WriteFile(
			"/etc/hostapd/hostapd.conf",
			[]byte(fmt.Sprintf(constant.ApTemplate, req.SSID, req.Password)),
			0640,
		)
		if err != nil {
			Response(w, r, http.StatusInternalServerError, err.Error(), nil)
			log.GetLogger().Errorln("update hostapd config error: ", err)
			return
		}
		switch req.Switch {
		case "down":
			err = exec.Command("systemctl", "stop", "hostapd").Run()
			if err != nil {
				log.GetLogger().Errorln("restart hostapd err:", err)
				Response(w, r, http.StatusInternalServerError, err.Error(), nil)
				return
			}
		case "up":
			err = exec.Command("systemctl", "restart", "hostapd").Run()
			if err != nil {
				log.GetLogger().Errorln("restart hostapd err:", err)
				Response(w, r, http.StatusInternalServerError, err.Error(), nil)
				return
			}
		}
		Response(w, r, http.StatusOK, ResponseMsgSuccess, nil)
		break
	default:
		Response(w, r, http.StatusBadRequest, "method not supported", nil)
	}
}

type wifiListItem struct {
	SSID       string
	Signal     float64
	Encryption bool
}

func wifiList(w http.ResponseWriter, r *http.Request) {
	wifiLock.Lock()
	defer wifiLock.Unlock()
	switch r.Method {
	case http.MethodGet:
		resp := make([]wifiListItem, 0)
		for i := 0; i < 3; i++ {
			cmd := `iwlist wlan0 scan`
			output, err := exec.Command("bash", "-c", cmd).Output()
			if err != nil {
				Response(w, r, http.StatusInternalServerError, err.Error(), nil)
				return
			}
			ssidMap := make(map[string]wifiListItem)
			if strings.Contains(string(output), `Scan completed`) {
				split := strings.Split(string(output), ` Cell `)
				for j, s := range split {
					if j == 0 {
						continue
					}
					line := strings.Split(s, "\n")
					SSID := ""
					Signal := float64(0)
					Encryption := false
					for _, s2 := range line {
						l := strings.TrimSpace(s2)
						if strings.HasPrefix(l, `ESSID:"`) {
							SSID = strings.Replace(l, `ESSID:"`, "", 1)
							SSID, _ = strings.CutSuffix(SSID, `"`)
							SSID = strings.ReplaceAll(SSID, `"`, `\"`)
							SSID, _ = strconv.Unquote(`"` + SSID + `"`)
						}
						if strings.HasPrefix(l, `Quality=`) {
							Signal, _ = strconv.ParseFloat(
								strings.ReplaceAll(strings.Split(l, `level=`)[1], " dBm", ""),
								64,
							)
						}
						if strings.HasPrefix(l, `Encryption key:`) {
							if l == "Encryption key:on" {
								Encryption = true
							}
						}
					}
					if SSID == "" {
						continue
					}
					if ssidMap[SSID].Signal == 0 || ssidMap[SSID].Signal < Signal {
						ssidMap[SSID] = wifiListItem{
							SSID:       SSID,
							Signal:     Signal,
							Encryption: Encryption,
						}
					}
				}
				for _, item := range ssidMap {
					resp = append(resp, item)
				}
				sort.Slice(resp, func(i, j int) bool {
					return resp[i].Signal > resp[j].Signal
				})
				if len(resp) > 0 {
					break
				}
			}
		}
		Response(w, r, http.StatusOK, ResponseMsgSuccess, resp)
	default:
		Response(w, r, http.StatusBadRequest, "method not supported", nil)
	}
}
