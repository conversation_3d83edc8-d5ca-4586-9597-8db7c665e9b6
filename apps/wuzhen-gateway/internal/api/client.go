package api

import (
	"gateway/internal/db"
	_interface "gateway/internal/interface"
	"gateway/internal/model"
	"net/http"
)

type clientListData struct {
	model.Client
	Proxy  string `json:"proxy"  gorm:"-"`
	Active bool   `json:"active" gorm:"-"`
}

func clientList(w http.ResponseWriter, r *http.Request) {
	getDb := db.GetDb()
	if r.Method == http.MethodGet {
		data := make([]*clientListData, 0)
		ipRules := make([]*model.IpRule, 0)
		getDb.Model(&model.IpRule{}).Find(&ipRules)
		m := make(map[string]string)
		for _, _ipRule := range ipRules {
			m[_ipRule.IP] = _ipRule.Proxy
		}
		getDb.Model(&model.Client{}).Find(&data)
		for i, d := range data {
			if _, ok := _interface.StatusApi.GetActiveIpMap().Load(d.IP); ok {
				data[i].Active = true
			}
			data[i].Proxy = m[d.IP]
			if data[i].Proxy == "" {
				data[i].Proxy = model.ProxyAuto
			}
		}
		Response(w, r, http.StatusOK, ResponseMsgSuccess, &data)
	}
}
