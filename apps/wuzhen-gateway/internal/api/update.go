package api

import (
	"fmt"
	"gateway/internal/config"
	"gateway/internal/db"
	"gateway/internal/log"
	"gateway/internal/platformRequest"
	"io"
	"net/http"
	"os"
	"os/exec"
	"time"

	"github.com/metacubex/mihomo/hub/executor"
)

func confirmUpdate(w http.ResponseWriter, r *http.Request) {
	if r.Method == http.MethodPost {
		_, err := os.Stat("/usr/xrouter/xrouter_new")
		if err != nil {
			Response(w, r, http.StatusInternalServerError, "not found new version", nil)
		}
		Response(w, r, http.StatusOK, ResponseMsgSuccess, nil)
		go func() {
			time.Sleep(time.Second)
			log.GetLogger().Infoln("updateSelf")
			_ = db.Close()
			executor.Shutdown()
			_ = exec.Command("reboot").Run()
		}()
	} else {
		Response(w, r, http.StatusBadRequest, "method not supported", nil)
	}
}
func updateLogs(w http.ResponseWriter, r *http.Request) {
	if r.Method == http.MethodGet {
		httpClient := &http.Client{
			Timeout: time.Second * 5,
		}
		request, err := platformRequest.NewRequest(
			"GET",
			fmt.Sprintf("%s/gateway/version", config.Config().CloudGateway),
			nil,
		)
		if err != nil {
			Response(w, r, http.StatusInternalServerError, err.Error(), nil)
			return
		}
		resp, err := httpClient.Do(request)
		if err != nil {
			Response(w, r, http.StatusInternalServerError, err.Error(), nil)
			return
		}
		defer func() { _ = resp.Body.Close() }()
		w.Header().Set("Content-Type", "application/json")
		_, _ = io.Copy(w, resp.Body)
	} else {
		Response(w, r, http.StatusBadRequest, "method not supported", nil)
	}
}
