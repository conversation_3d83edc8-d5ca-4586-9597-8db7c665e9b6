package api

import (
	"encoding/json"
	"gateway/internal/clash"
	"gateway/internal/config"
	"gateway/internal/db"
	_interface "gateway/internal/interface"
	"gateway/internal/log"
	"gateway/internal/model"
	"io"
	"net/http"
)

type ipRuleData struct {
	Ip    string `json:"ip"`
	Proxy string `json:"proxy"`
	Wg    bool   `json:"wg"`
}

func ipRuleList(w http.ResponseWriter, r *http.Request) {
	getDb := db.GetDb()
	if r.Method == http.MethodGet {
		list := make([]*ipRuleData, 0)
		getDb.Model(&model.IpRule{}).Find(&list)
		Response(w, r, http.StatusOK, ResponseMsgSuccess, &list)
	}
}

func ipRule(w http.ResponseWriter, r *http.Request) {
	model.ProxiesLock.Lock()
	defer model.ProxiesLock.Unlock()
	getDb := db.GetDb()
	var data ipRuleData
	err := json.NewDecoder(r.Body).Decode(&data)
	if err != nil && err != io.EOF {
		Response(w, r, http.StatusBadRequest, err.Error(), nil)
		return
	}
	data.Ip = r.Context().Value("ip").(string)
	if r.Method == http.MethodPost {
		if data.Wg {
			data.Wg = false
			if config.HasFlag(config.FlagWgEnable) {
				data.Wg = true
			}
		}
		proxy := model.Proxies{}
		_path := model.Path{}
		_ipRule := model.IpRule{}
		getDb.Model(&model.IpRule{}).Where("ip = ?", data.Ip).First(&_ipRule)
		err = getDb.Model(&model.Proxies{}).Where("name = ?", data.Proxy).First(&proxy).Error
		if err != nil { //代理不存在
			err = getDb.Model(&model.Path{}).Where("name = ?", data.Proxy).First(&_path).Error
			if err != nil {
				Response(w, r, http.StatusNotFound, "proxy not found", nil)
				return
			}
		} else { //代理存在
			if proxy.UseStatus == model.ProxyUseStatusExclusive {
				Response(w, r, http.StatusBadRequest, "proxy is exclusive", nil)
				return
			}
			if proxy.UseStatus == model.ProxyUseStatusDiscard {
				Response(w, r, http.StatusBadRequest, "proxy is discard", nil)
				return
			}
		}
		//如果传入的链路名那么就会更新链路
		if data.Proxy == _path.Name {
			err = model.AddOrUpdateIpRule(getDb, data.Ip, data.Proxy, data.Wg)
			if err != nil {
				Response(w, r, http.StatusNotFound, "path not found", nil)
				return
			}
			model.CheckPathUse(getDb)
		} else { //如果传入的是节点名那么就会更新当前链路的出口
			if proxy.Type != model.ProxyTypeExitProxy {
				Response(w, r, http.StatusBadRequest, "proxy is not exit proxy", nil)
				return
			}
			myPath := model.Path{}
			err = getDb.Model(&model.Path{}).Where("name = ?", _ipRule.Proxy).First(&myPath).Error
			if err != nil {
				Response(w, r, http.StatusNotFound, "path not found", nil)
				return
			}
			myPath.Proxies[len(myPath.Proxies)-1] = data.Proxy
			//myPath.Exclusive = model.PathExclusiveNone
			err = model.AddOrUpdatePath(getDb, &myPath, "", true)
			if err != nil {
				Response(w, r, http.StatusNotFound, err.Error(), nil)
				return
			}
			model.CheckPathUse(getDb)
		}
		model.CheckProxiesUse(getDb)
		err = clash.HotUpdate()
		if err != nil {
			log.GetLogger().Errorf("hot update error: %s", err.Error())
			Response(w, r, http.StatusInternalServerError, "hot update error", nil)
			return
		}
		_interface.ClashApi.CloseSourceIpConnection(data.Ip)
		Response(w, r, http.StatusOK, ResponseMsgSuccess, nil)
	}
	if r.Method == http.MethodDelete {
		err = model.DeleteIpRule(getDb, data.Ip)
		err = clash.HotUpdate()
		if err != nil {
			log.GetLogger().Errorf("hot update error: %s", err.Error())
			Response(w, r, http.StatusInternalServerError, "hot update error", nil)
			return
		}
		model.CheckProxiesUse(getDb)
		Response(w, r, http.StatusOK, ResponseMsgSuccess, nil)
	}
	if r.Method == http.MethodGet {
		res := ipRuleData{}
		_ipRule := model.GetIpRule(getDb, data.Ip)
		if _ipRule.Proxy == "" {
			_ = model.AddOrUpdateIpRule(getDb, data.Ip, _interface.ClashApi.GetGetAutoNow(), false)
			_ipRule = model.GetIpRule(getDb, data.Ip)
			err = clash.HotUpdate()
			if err != nil {
				log.GetLogger().Errorf("hot update error: %s", err.Error())
				Response(w, r, http.StatusInternalServerError, "hot update error", nil)
				return
			}
		}
		res.Proxy = _ipRule.Proxy
		res.Ip = data.Ip
		Response(w, r, http.StatusOK, ResponseMsgSuccess, &res)
	}
}
