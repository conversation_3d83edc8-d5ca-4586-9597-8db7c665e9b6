package api

import (
	"encoding/json"
	"gateway/internal/db"
	"gateway/internal/model"
	"net/http"

	"github.com/gorilla/mux"
)

type storageData interface{}

func storage(w http.ResponseWriter, r *http.Request) {
	var (
		key    string
		exists bool
	)
	vars := mux.Vars(r)
	if key, exists = vars["key"]; !exists {
		Response(w, r, http.StatusBadRequest, "missing parameters", nil)
		return
	}
	switch r.Method {
	case http.MethodPost:
		// 创建/更新存储值
		var req storageData
		err := json.NewDecoder(r.Body).Decode(&req)
		if err != nil {
			Response(w, r, http.StatusBadRequest, "invalid parameters format", nil)
			return
		}
		m := model.NewStorageModel(db.GetDb())
		err = m.Set(key, req)
		if err != nil {
			Response(w, r, http.StatusInternalServerError, err.Error(), nil)
			return
		}
		Response(w, r, http.StatusOK, ResponseMsgSuccess, nil)
		break
	case http.MethodGet:
		// 获取存储值
		m := model.NewStorageModel(db.GetDb())
		value, err := m.Get(key)
		if err != nil {
			Response(w, r, http.StatusInternalServerError, err.Error(), nil)
			return
		}
		Response(w, r, http.StatusOK, ResponseMsgSuccess, value)
		break
	case http.MethodDelete:
		// 删除存储值
		m := model.NewStorageModel(db.GetDb())
		err := m.Del(key)
		if err != nil {
			Response(w, r, http.StatusInternalServerError, err.Error(), nil)
			return
		}
		Response(w, r, http.StatusOK, ResponseMsgSuccess, nil)
		break
	default:
		Response(w, r, http.StatusBadRequest, "method not supported", nil)
	}
}
