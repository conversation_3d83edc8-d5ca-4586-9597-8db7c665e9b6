package api

import (
	"gateway/internal/clash"
	"gateway/internal/config"
	"gateway/internal/middleware"
	"net/http"

	"github.com/go-chi/render"
	"github.com/gorilla/mux"
)

type Msg struct {
	Msg  string      `json:"msg"`
	Code int         `json:"code"`
	Data interface{} `json:"data,omitempty"`
}

var ResponseMsgSuccess = "success"

func Response(w http.ResponseWriter, r *http.Request, code int, msg string, data interface{}) {
	render.Status(r, code)
	var response = Msg{
		Code: code,
		Msg:  msg,
		Data: data,
	}
	render.JSON(w, r, &response)
}

func Register(root *mux.Router) {
	root.Use(middleware.Ip)
	if config.Config().Dev {
		root.Handle("/debug/config", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			_, _ = w.Write(clash.Config())
		}))
	}
	api := root.PathPrefix("/api").Subrouter()
	// 不需要登录的接口
	api.Handle("/login", http.HandlerFunc(login))
	api.Handle("/register", http.HandlerFunc(register))
	api.Handle("/status", http.HandlerFunc(statusH))
	// 需要登录的接口
	userR := api.PathPrefix("/").Subrouter()
	userR.Use(middleware.UserAuth)
	{
		userR.Handle("/info", middleware.InitPath(http.HandlerFunc(info)))
		userR.Handle("/proxies", http.HandlerFunc(proxies))
		userR.Handle("/ipRuleList", http.HandlerFunc(ipRuleList))
		userR.Handle("/ipRule", http.HandlerFunc(ipRule))
		userR.Handle("/pathList", http.HandlerFunc(pathList))
		userR.Handle("/path", http.HandlerFunc(path))
		userR.Handle("/traffic", http.HandlerFunc(traffic))
		userR.Handle("/delay", http.HandlerFunc(delay))
		userR.Handle("/user/info", http.HandlerFunc(userInfo))
		userR.Handle("/user/password", http.HandlerFunc(userPassword))
		userR.Handle("/screen", http.HandlerFunc(screen))
		userR.Handle("/storage/{key}", http.HandlerFunc(storage))
		userR.Handle("/proxies/fastest", http.HandlerFunc(fastestProxy))

	}
	adminR := userR.PathPrefix("/").Subrouter()
	adminR.Use(middleware.AdminAuth, middleware.InitPath)
	{
		// 管理员权限
		adminR.Handle("/user", http.HandlerFunc(user))
		adminR.Handle("/user/list", http.HandlerFunc(userList))
		adminR.Handle("/user/{id}", http.HandlerFunc(user))
		adminR.Handle("/user/password/{id}", http.HandlerFunc(userPassword))
		if config.HasFlag(config.FlagEquipmentEnable) {
			adminR.Handle("/clientList", http.HandlerFunc(clientList))
		}
		adminR.Handle("/confirmUpdate", http.HandlerFunc(confirmUpdate))
		adminR.Handle("/updateLogs", http.HandlerFunc(updateLogs))
	}
}
