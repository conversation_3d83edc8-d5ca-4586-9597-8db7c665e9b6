package api

import (
	"encoding/json"
	"fmt"
	"gateway/internal/config"
	"gateway/internal/db"
	_interface "gateway/internal/interface"
	"gateway/internal/log"
	"gateway/internal/model"
	"gateway/internal/platformRequest"
	"gateway/internal/utils"
	"io"
	"net/http"
	"time"

	_ "github.com/wzshiming/shadowsocks/init"
)

func fastestProxy(w http.ResponseWriter, r *http.Request) {
	if r.Method == http.MethodGet {
		ingress, _ := _interface.ClashApi.GetAutoPassGFWNow()
		relay := _interface.ClashApi.GetAutoGuardNow()
		egress := _interface.ClashApi.GetGetAutoNow()
		Response(w, r, http.StatusOK, ResponseMsgSuccess, []string{ingress, relay, egress})
	}
}

type proxiesData struct {
	Name        string `json:"name"`
	Server      string `json:"server"`
	Port        int    `json:"port"`
	Protocol    string `json:"protocol"`
	CountryName string `json:"country_name"`
	CountryCode string `json:"country_code"`
	CityName    string `json:"city_name"`
	Delay       int    `json:"delay"`
	UseStatus   string `json:"use_status"`
	Type        string `json:"type"`
	Source      string `json:"source"`
}

func proxies(w http.ResponseWriter, r *http.Request) {
	model.ProxiesLock.Lock()
	defer model.ProxiesLock.Unlock()
	getDb := db.GetDb()
	if r.Method == http.MethodGet {
		Type := r.URL.Query().Get("type")
		list := make([]*proxiesData, 0)
		if Type != "" {
			getDb.Model(&model.Proxies{}).Where("type =?", Type).Order("name ASC").Find(&list)
		} else {
			getDb.Model(&model.Proxies{}).Where("type IN (?)", []string{model.ProxyTypePassGfw, model.ProxyTypeExitProxy, model.ProxyTypeGuardProxy}).Order("name ASC").Find(&list)
		}
		for i := range list {
			list[i].Server = utils.CodingIp(list[i].Server)
		}
		Response(w, r, http.StatusOK, ResponseMsgSuccess, &list)
	}

	if r.Method == http.MethodDelete {
		if config.HasFlag(config.FlagDiscardEnable) {
			discardProxies(w, r)
		}
	}
}

type discardProxiesData struct {
	Name string `json:"name"`
}

func discardProxies(w http.ResponseWriter, r *http.Request) {
	data := discardProxiesData{}
	err := json.NewDecoder(r.Body).Decode(&data)
	if err != nil && err != io.EOF {
		Response(w, r, http.StatusBadRequest, err.Error(), nil)
		return
	}
	getDb := db.GetDb()
	proxy := model.GetProxy(getDb, data.Name)
	if proxy.Name == "" {
		Response(w, r, http.StatusBadRequest, "proxy not found", nil)
		return
	}
	if proxy.Type != model.ProxyTypeExitProxy {
		Response(w, r, http.StatusBadRequest, "proxy type is not exit proxy", nil)
		return
	}
	if proxy.UseStatus != model.ProxyUseStatusNone {
		Response(w, r, http.StatusBadRequest, "proxy is in use", nil)
		return
	}
	// 更新节点状态
	err = getDb.Model(model.Proxies{}).
		Where("name = ?", data.Name).
		Update("use_status", model.ProxyUseStatusDiscard).
		Error
	if err != nil {
		Response(w, r, http.StatusBadRequest, err.Error(), nil)
		return
	}
	Response(w, r, http.StatusOK, ResponseMsgSuccess, nil)
}

type importingProxiesData struct {
	Data string `json:"data"`
}

type ipInfoRes struct {
	Ip       string `json:"ip"`
	Hostname string `json:"hostname"`
	City     string `json:"city"`
	Region   string `json:"region"`
	Country  string `json:"country"`
	Loc      string `json:"loc"`
	Org      string `json:"org"`
	Timezone string `json:"timezone"`
	Readme   string `json:"readme"`
}
type ipSbRes struct {
	Organization    string  `json:"organization"`
	Longitude       float64 `json:"longitude"`
	Timezone        string  `json:"timezone"`
	Isp             string  `json:"isp"`
	Offset          int     `json:"offset"`
	Asn             int     `json:"asn"`
	AsnOrganization string  `json:"asn_organization"`
	Country         string  `json:"country"`
	Ip              string  `json:"ip"`
	Latitude        float64 `json:"latitude"`
	ContinentCode   string  `json:"continent_code"`
	CountryCode     string  `json:"country_code"`
}

type hitDeployReq struct {
	CountryCode       string `json:"country_code"`
	VulnerabilityType string `json:"vulnerability_type"`
}

type cloudData struct {
	Code int         `json:"code"`
	Data interface{} `json:"data"`
	Msg  string      `json:"msg"`
}
type hitDeployResp struct {
	Proxies []map[string]any `json:"proxies"`
}

func removeDuplicates(input []map[string]any) []map[string]any {
	seen := make(map[any]bool)
	var result []map[string]any
	for _, value := range input {
		if !seen[value["name"]] {
			seen[value["name"]] = true
			result = append(result, value)
		}
	}
	return result
}

type hitDeployCountryData struct {
	Country []countryItem `json:"country"`
}

type countryItem struct {
	Code string `json:"code"`
	Name string `json:"name"`
}

func hitDeployCountry(w http.ResponseWriter, r *http.Request) {
	switch r.Method {
	case http.MethodGet:
		// 从平台拉取打击部署国家数据
		httpClient := &http.Client{
			Timeout: time.Second * 5,
		}
		request, err := platformRequest.NewRequest(
			"GET",
			fmt.Sprintf("%s/hit-deploy/country", config.Config().CloudGateway),
			nil,
		)
		if err != nil {
			Response(w, r, http.StatusInternalServerError, err.Error(), nil)
			return
		}

		resp, err := httpClient.Do(request)
		if err != nil {
			log.GetLogger().Errorf("get platform /hit-deploy/country fail: %v", err)
			Response(w, r, http.StatusInternalServerError, err.Error(), nil)
			return
		}
		defer resp.Body.Close()
		var respData cloudData
		err = json.NewDecoder(resp.Body).Decode(&respData)
		if err != nil {
			Response(w, r, http.StatusInternalServerError, err.Error(), nil)
			return
		}
		Response(w, r, http.StatusOK, ResponseMsgSuccess, respData.Data)

	default:
		Response(w, r, http.StatusBadRequest, "method not supported", nil)
	}
}
