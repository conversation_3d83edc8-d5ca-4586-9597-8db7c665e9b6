package api

import (
	"encoding/json"
	"gateway/internal/clash"
	"gateway/internal/db"
	_interface "gateway/internal/interface"
	"gateway/internal/log"
	"gateway/internal/model"
	"io"
	"net/http"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type pathListData struct {
	Name               string     `json:"name"`
	Proxies            []string   `json:"proxies"              gorm:"serializer:json;comment:路径列表"`
	Use                bool       `json:"use"`
	ProxiesCode        []string   `json:"proxies_code"         gorm:"-"`
	Exclusive          string     `json:"exclusive"`
	Account            string     `json:"account"`
	AccountIsAdmin     bool       `json:"account_is_admin"`
	CurrentIPUse       bool       `json:"current_ip_use"`
	ChangeTime         uint64     `json:"change_time"`
	ChangeCountryArray [][]string `json:"change_country_array" gorm:"serializer:json"`
}

func pathList(w http.ResponseWriter, r *http.Request) {
	getDb := db.GetDb()
	if r.Method == http.MethodGet {
		ip := r.Context().Value("ip").(string)
		_user := r.Context().Value("user").(model.User)
		list := getPathList(getDb, _user)
		rule := model.GetIpRule(getDb, ip)
		_path := model.Path{}
		getDb.Where("name = ?", rule.Proxy).First(&_path)
		paths := make([]*pathListData, 0)
		_proxies := make([]*proxiesData, 0)
		proxiesCode := make([]string, 0)
		getDb.Model(&model.Proxies{}).Where("name IN (?)", _path.Proxies).Find(&_proxies)
		for _, proxy := range _path.Proxies {
			for _, p := range _proxies {
				if p.Name == proxy {
					proxiesCode = append(proxiesCode, p.CountryCode)
				}
			}
		}
		paths = append(paths, &pathListData{
			Name:               _path.Name,
			Proxies:            _path.Proxies,
			Use:                true,
			ProxiesCode:        proxiesCode,
			Exclusive:          _path.Exclusive,
			Account:            _user.Account,
			AccountIsAdmin:     _user.IsAdmin,
			CurrentIPUse:       true,
			ChangeTime:         _path.ChangeTime,
			ChangeCountryArray: _path.ChangeCountryArray,
		})
		for _, data := range list {
			if data.Name != _path.Name {
				paths = append(paths, data)
			}
		}
		Response(w, r, http.StatusOK, ResponseMsgSuccess, paths)
	}
}
func getPathList(getDb *gorm.DB, u model.User) []*pathListData {
	_proxies := make([]*model.Proxies, 0)
	getDb.Model(&model.Proxies{}).Find(&_proxies)
	list := make([]*pathListData, 0)
	if u.IsAdmin {
		getDb.Raw("SELECT u.is_admin as account_is_admin,u.account,p.exclusive,p.name,p.proxies,p.change_time,p.change_country_array,CASE WHEN (SELECT count(1) FROM ip_rule WHERE proxy = p.name) > 0 THEN 1  ELSE 0  END AS use FROM path as p,user as u WHERE p.user_id = u.id AND ((SELECT COUNT(1) FROM ip_rule WHERE proxy = p.name) > 0 OR u.id=?) ORDER BY use,name ASC", u.ID).
			Find(&list)
		temp := make([]*pathListData, 0)
		for _, data := range list {
			if data.Account == u.Account {
				temp = append(temp, data)
			}
		}
		for _, data := range list {
			if data.Account != u.Account {
				temp = append(temp, data)
			}
		}
		list = temp
	} else {
		getDb.Raw("SELECT u.account,p.exclusive,p.name,p.proxies,p.change_time,p.change_country_array,CASE WHEN (SELECT count(1) FROM ip_rule WHERE proxy = p.name) > 0 THEN 1  ELSE 0  END AS use FROM path as p,user as u WHERE p.user_id = u.id AND p.user_id = ?  ORDER BY use,name ASC", u.ID).Find(&list)
	}
	pCode := make(map[string]string)
	for _, proxy := range _proxies {
		pCode[proxy.Name] = proxy.CountryCode
	}
	for i, data := range list {
		list[i].ProxiesCode = make([]string, 0)
		for _, proxy := range data.Proxies {
			list[i].ProxiesCode = append(list[i].ProxiesCode, pCode[proxy])
		}
	}
	return list
}

type pathData struct {
	model.Path
	NewName string `json:"new_name"`
	Create  bool   `json:"create"`
	Apply   bool   `json:"apply"`
}

func path(w http.ResponseWriter, r *http.Request) {
	getDb := db.GetDb()
	model.ProxiesLock.Lock()
	defer model.ProxiesLock.Unlock()
	u := r.Context().Value("user").(model.User)
	ip := r.Context().Value("ip").(string)
	var data pathData
	err := json.NewDecoder(r.Body).Decode(&data)
	if err != nil && err != io.EOF {
		Response(w, r, http.StatusBadRequest, err.Error(), nil)
		return
	}
	if r.Method == http.MethodPost {
		if len(data.Proxies) == 0 {
			Response(w, r, http.StatusBadRequest, "proxies empty", nil)
			return
		}
		count := int64(0)
		getDb.Model(&model.Proxies{}).
			Where("name = ? OR name = ?", data.Name, data.NewName).
			Count(&count)
		if count > 0 {
			Response(w, r, http.StatusBadRequest, "name unavailable", nil)
			return
		}
		//检查path
		//setServerIP := make(map[string]struct{})
		for _, proxy := range data.Proxies {
			if proxy == model.ProxyAuto || proxy == model.ProxyAutoGuard {
				Response(w, r, http.StatusBadRequest, "auto not use in path", nil)
				return
			}
			p := model.Proxies{}
			err = getDb.Model(&model.Proxies{}).Where("name = ?", proxy).First(&p).Error
			if err != nil {
				Response(w, r, http.StatusNotFound, "proxy not found", nil)
				return
			}
			if p.UseStatus == model.ProxyUseStatusDiscard {
				Response(w, r, http.StatusBadRequest, "proxy is discard", nil)
				return
			}
			//if _, ok := setServerIP[p.Server]; ok {
			//	Response(w, r, http.StatusBadRequest, "path loop", nil)
			//	return
			//}
			//setServerIP[p.Server] = struct{}{}
		}

		//校验path name
		getPath := model.GetPath(getDb, data.Name)
		if data.Create {
			if getPath.Name != "" {
				data.Name += uuid.New().String()[:4]
				//Response(w, r, http.StatusBadRequest, "name unavailable", nil)
				//return
			}
		}
		//校验path权限
		if getPath.Name != "" && getPath.UserID != u.ID && u.IsAdmin == false {
			Response(w, r, http.StatusBadRequest, "no permission", nil)
			return
		}
		//校验path name
		if data.NewName != "" {
			getPath = model.GetPath(getDb, data.NewName)
			if getPath.Name != "" {
				Response(w, r, http.StatusBadRequest, "name unavailable", nil)
				return
			}
		}
		data.Path.UserID = u.ID
		useCount := model.GetPathUseCount(getDb, data.Name)
		err = model.AddOrUpdatePath(getDb, &data.Path, data.NewName, data.Apply || useCount > 0)
		if err != nil {
			Response(w, r, http.StatusInternalServerError, err.Error(), nil)
			return
		}
		if data.Apply {
			err = model.AddOrUpdateIpRule(getDb, ip, data.Name, false)
			if err != nil {
				Response(w, r, http.StatusInternalServerError, err.Error(), nil)
				return
			}
		}
		if useCount > 0 || data.Apply {
			err = clash.HotUpdate()
			if err != nil {
				Response(w, r, http.StatusInternalServerError, err.Error(), nil)
				return
			}
			_interface.ClashApi.CloseSourceIpConnection(ip)
		}
		model.CheckPathUse(getDb)
		model.CheckProxiesUse(getDb)
		Response(w, r, http.StatusOK, ResponseMsgSuccess, nil)
	}
	if r.Method == http.MethodDelete {
		err = getDb.Model(&model.Path{}).Where("name = ?", data.Name).First(&data.Path).Error
		if err != nil {
			Response(w, r, http.StatusInternalServerError, "path not found", nil)
			return
		}
		if data.Path.UserID != u.ID && u.IsAdmin == false {
			Response(w, r, http.StatusBadRequest, "no permission", nil)
			return
		}
		count := int64(0)
		getDb.Model(&model.IpRule{}).Where("proxy = ?", data.Name).Count(&count)
		if count > 0 {
			Response(w, r, http.StatusBadRequest, "path in use", nil)
			return
		}
		getDb.Model(&model.Path{}).Where("name = ?", data.Name).Delete(&model.Path{})
		w.WriteHeader(http.StatusOK)
		err = clash.HotUpdate()
		if err != nil {
			log.GetLogger().Errorf("hot update error: %s", err.Error())
			Response(w, r, http.StatusInternalServerError, "hot update error", nil)
			return
		}
		model.CheckProxiesUse(getDb)
		Response(w, r, http.StatusOK, ResponseMsgSuccess, nil)
	}
}
