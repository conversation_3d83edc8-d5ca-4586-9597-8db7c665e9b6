package sub

import (
	"encoding/json"
	"fmt"
	"gateway/internal/clash"
	"gateway/internal/config"
	"gateway/internal/db"
	"gateway/internal/log"
	"gateway/internal/model"
	"gateway/internal/platformRequest"
	"io"
	"net/http"
	"strings"
	"time"
)

var lastData []byte

type cloudConfig struct {
	Code int    `json:"code"`
	Data data   `json:"data"`
	Msg  string `json:"msg"`
}

type data struct {
	ByPassGfw []map[string]any `json:"by_pass_gfw"`
	Exit      []map[string]any `json:"exit"`
	Guard     []map[string]any `json:"guard_list"`
}

func StartSub() {
	httpClient := &http.Client{
		Timeout: time.Second * 5,
	}
	update := do(httpClient)
	if update {
		log.GetLogger().Infoln("Get config,HotUpdate")
	}
	go func() {
		for {
			time.Sleep(time.Minute)
			if do(httpClient) {
				log.GetLogger().Infoln("Get config,HotUpdate")
				err := clash.HotUpdate()
				if err != nil {
					log.GetLogger().Errorf("HotUpdate error: %s", err.Error())
				}
			}
		}
	}()
}

func do(client *http.Client) bool {
	proxies := make([]string, 0)
	db.GetDb().
		Model(&model.Proxies{}).
		Select("name").
		Where("type = ? AND use_status=? AND source = ?", model.ProxyTypeExitProxy, false, model.ProxyUseStatusDiscard, model.ProxySourcePlatform).
		Find(&proxies)

	request, err := platformRequest.NewRequest(
		"GET",
		fmt.Sprintf("%s/config", config.Config().CloudGateway),
		nil,
	)
	if err != nil {
		log.GetLogger().Errorf("Get config error: %s", err.Error())
		return false
	}
	resp, err := client.Do(request)
	if err != nil {
		log.GetLogger().Errorf("Get config error: %s", err.Error())
		return false
	}
	defer func() { _ = resp.Body.Close() }()
	all, err := io.ReadAll(resp.Body)
	if err != nil {
		log.GetLogger().Errorf("Read config error: %s", err.Error())
		return false
	}
	c := cloudConfig{}
	err = json.Unmarshal(all, &c)
	if err != nil {
		log.GetLogger().Errorf("Unmarshal config error: %s", err.Error())
		return false
	}

	if string(lastData) == string(all) {
		return false
	}
	lastData = all
	passGfwList := make([]map[string]any, 0)
	passGfwList = appendProxies(passGfwList, c.Data.ByPassGfw...)
	guardList := make([]map[string]any, 0)
	guardList = appendProxies(guardList, c.Data.Guard...)
	exitList := make([]map[string]any, 0)
	exitList = appendProxies(exitList, c.Data.Exit...)

	passGfwList = append(passGfwList, config.Config().PassGfwList...)
	exitList = append(exitList, config.Config().ExitList...)
	guardList = append(guardList, config.Config().GuardList...)

	clash.UpdateProxyList(
		db.GetDb(),
		removeDuplicates(passGfwList),
		removeDuplicates(guardList),
		removeDuplicates(exitList),
	)
	return true
}
func removeDuplicates(input []map[string]any) []map[string]any {
	seen := make(map[any]bool)
	var result []map[string]any
	for _, value := range input {
		if !seen[value["name"]] {
			seen[value["name"]] = true
			result = append(result, value)
		}
	}
	return result
}

func appendProxies(proxies []map[string]any, origin ...map[string]any) []map[string]any {
	for i, s := range origin {
		var s1, s2 string
		var id float64
		if val, ok := s["country_name_zh"].(string); !ok {
			continue
		} else {
			s1 = strings.TrimSpace(val)
		}
		if val, ok := s["city_name_zh"].(string); !ok {
			continue
		} else {
			s2 = strings.TrimSpace(val)
			if s1 != s2 {
				s1 = fmt.Sprintf("%s-%s", s1, s2)
			}
		}
		if val, ok := s["id"].(float64); !ok {
			continue
		} else {
			id = val
		}
		origin[i]["name"] = fmt.Sprintf("%s-%.f", s1, id)
		proxies = append(proxies, origin[i])
	}
	return proxies
}
