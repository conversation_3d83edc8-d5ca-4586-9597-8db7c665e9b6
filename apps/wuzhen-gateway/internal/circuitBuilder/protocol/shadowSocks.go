package protocol

import (
	"context"
	"errors"
	"net"
	"strings"

	shadowsocks "github.com/metacubex/sing-shadowsocks2"
	"github.com/sagernet/sing/common/bufio"
	"github.com/sagernet/sing/common/metadata"
)

type ShadowSocks struct {
	cipher shadowsocks.Method
}

func (s *ShadowSocks) DialConn(conn net.Conn, network string, address string) (net.Conn, error) {
	switch network {
	case "tcp":
		return s.cipher.DialConn(conn, metadata.ParseSocksaddr(address))
	case "udp":
		// TODO: UDP 支持有些问题，后续排查
		return bufio.NewBindPacketConn(
			s.cipher.DialPacketConn(conn),
			metadata.ParseSocksaddr(address),
		), nil
	default:
		return nil, errors.New("unknown network")
	}
}

func NewShadowSocks(method string, password string) (*ShadowSocks, error) {
	method = strings.ToLower(method)

	cipher, err := shadowsocks.CreateMethod(context.Background(), method, shadowsocks.MethodOptions{
		Password: password,
	})
	if err != nil {
		return nil, err
	}
	return &ShadowSocks{cipher: cipher}, nil
}
