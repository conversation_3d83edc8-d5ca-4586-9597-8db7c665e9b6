package circuitBuilder

import (
	"errors"
	"gateway/internal/circuitBuilder/protocol"
	"gateway/internal/model"
	"net"
	"strconv"
	"strings"

	"golang.org/x/net/proxy"
)

type protocolClient interface {
	DialConn(conn net.Conn, network string, address string) (net.Conn, error)
}

type Builder struct {
	proxies []model.Proxies
}

func NewBuilder() *Builder {
	return &Builder{}
}

func (b *Builder) Append(proxies ...model.Proxies) error {
	if len(proxies) == 0 {
		return errors.New("proxies is at least one")
	}
	b.proxies = append(b.proxies, proxies...)
	return nil
}

func (b *Builder) Build() (proxy.Dialer, error) {
	var d proxy.Dialer
	d = new(net.Dialer)
	for _, p := range b.proxies {
		switch p.Protocol {
		case "ss":
			cipher, ok := p.Raw["cipher"].(string)
			if !ok || cipher == "" {
				return nil, errors.New("cipher is required for ss protocol")
			}
			password, ok := p.Raw["password"].(string)
			if !ok || password == "" {
				return nil, errors.New("password is required for ss protocol")
			}
			ss, err := protocol.NewShadowSocks(cipher, password)
			if err != nil {
				return nil, err
			}
			d = NewDialer(d, p.Server+":"+strconv.FormatInt(p.Port, 10), ss)
		// 目前socks5都是打击节点，暂时不需要构建带有打击节点的电路
		//case "socks5":
		default:
			return nil, errors.New("protocol not supported")
		}
	}
	return d, nil
}

func (b *Builder) String() string {
	s := make([]string, len(b.proxies))
	for i, p := range b.proxies {
		s[i] = p.Name
	}
	return strings.Join(s, " -> ")
}

func (b *Builder) Slice() []string {
	s := make([]string, len(b.proxies))
	for i, p := range b.proxies {
		s[i] = p.Name
	}
	return s
}

type Dialer struct {
	transport    proxy.Dialer
	proxyAddress string
	protocol     protocolClient
}

func NewDialer(transport proxy.Dialer, proxyAddress string, protocol protocolClient) *Dialer {
	return &Dialer{transport: transport, protocol: protocol, proxyAddress: proxyAddress}
}

func (d *Dialer) Dial(network, addr string) (net.Conn, error) {
	c, err := d.transport.Dial("tcp", d.proxyAddress)
	if err != nil {
		return nil, err
	}
	return d.protocol.DialConn(c, network, addr)
}
