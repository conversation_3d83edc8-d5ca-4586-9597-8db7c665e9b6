package platformRequest

import (
	"bytes"
	"encoding/json"
	"fmt"
	"gateway/internal/config"
	"gateway/internal/db"
	"gateway/internal/log"
	"gateway/internal/model"
	"io"
	"net/http"
)

var (
	token string
)

type ActiveReq struct {
	DeviceCode string `json:"device_code"`
	Secret     string `json:"secret"`
}

type ActiveResp struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		Token string `json:"token"`
	} `json:"data"`
}

func Init() error {
	if token == "" {
		// token不存在, 尝试从db获取
		_token, exists := model.GetConfig(db.GetDb(), "token")
		if !exists {
			client := http.Client{}
			// 不存在则从远程获取
			var req = ActiveReq{
				DeviceCode: config.Config().DeviceCode,
				Secret:     config.Config().Secret,
			}
			jsonData, _ := json.Marshal(req)
			resp, err := client.Post(
				fmt.Sprintf("%s/active", config.Config().CloudGateway),
				"application/json",
				bytes.NewBuffer(jsonData),
			)
			if err != nil {
				log.GetLogger().Errorf("request token from platform error: %s", err.Error())
				return err
			}
			defer resp.Body.Close()
			var respData ActiveResp
			all, err := io.ReadAll(resp.Body)
			if err != nil {
				log.GetLogger().Errorf("request token from platform error: %s", err.Error())
			}
			err = json.Unmarshal(all, &respData)
			if err != nil {
				log.GetLogger().
					Errorf("request token from platform error: %s data:%s", err.Error(), string(all))
				return err
			}
			token = respData.Data.Token
			model.SetConfig(db.GetDb(), "token", token)
		} else {
			token = _token
		}
	}
	return nil
}

func GetToken() string {
	return token
}

func NewRequest(method string, url string, data interface{}) (*http.Request, error) {
	// 将数据编码为 JSON 格式
	if data == nil {
		data = map[string]interface{}{}
	}
	jsonData, err := json.Marshal(data)
	if err != nil {
		log.GetLogger().Errorf("NewRequest:error marshalling JSON: %v", err)
		return nil, fmt.Errorf("error marshalling JSON: %v", err)
	}
	// 创建新的 HTTP 请求
	req, err := http.NewRequest(method, url, bytes.NewBuffer(jsonData))
	if err != nil {
		log.GetLogger().Errorf("NewRequest:error creating request: %v", err)
		return nil, fmt.Errorf("error creating request: %v", err)
	}
	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Token", token)
	return req, nil
}
