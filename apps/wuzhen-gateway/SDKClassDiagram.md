```mermaid
---
title: Wuzhen V2 SDK
---
classDiagram
    class WuzhenV2SDK {
        + New(Option) WuzhenV2SDK
    }
    WuzhenV2SDK --o CircuitGetter: contains
    WuzhenV2SDK --o CountriesGetter: contains

    Options o-- WuzhenV2SDK: uses
    class Options {
        + WithDeviceAuth(deviceCode string, secret string) Options
        + WithAntiCensorship() Options
    }

%% CircuitGetter 类提供了获取电路的接口，外部用户可以通过该接口获取电路
    class CircuitGetter {
        + GetCircuit(UserContext) Circuit
    }
    <<interface>> CircuitGetter
    CircuitGetter <|.. ClientForDevice: implements
    CircuitGetter --o UserContext: uses

    class ClientForDevice {
        - bool antiCensorship
        - CircuitCache circuitCache
        + GetCircuit(UserContext) Circuit
    }
    ClientForDevice --o CircuitSet: contains
%% 管理来源偏好与电路的映射关系
    class CircuitSet {
        - map[UserContext]circuitCacheValue circuitCache
        - lock sync.RWMutex
        + Load(UserContext) Circuit
        + Save(UserContext, Circuit)
    }
    CircuitSet --o circuitSetValue: contains
    CircuitSet --o CircuitBuilder: uses
    class circuitSetValue {
        - Circuit circuit
        - time.Time expireTime
        - isExpired() bool
        - resetExpireTime()
    }

    class CircuitBuilder {
        - bool antiCensorship
        + Build(Pref pref) Circuit
    }
    CircuitBuilder --* Circuit
%% Circuit 是一个抽象的电路，包含多个代理节点
    class Circuit {
        - []Proxy proxies
        - net.Conn conn
        - Pref pref
        + GetConn() net.Conn
        + reBuild()
    }
    CircuitBuilder --o ProxiesGetter: uses
%% UserContext 是一个抽象的元数据，包含来源地址以及跳转偏好
    class UserContext {
        - string identity
        - Pref pref
    }
    UserContext --o Pref: contains

    class Pref {
        - []string countries
        - bool isJumpChange
        + IsJumpChange() bool
        + GetCountries() []string
        + New(contries []string)
        + WithJumpChange() Pref
    }

%% TODO 这里的ProxyPool实现了两个接口，一个暴露给用户，一个用于内部使用，是否合理
%% CountriesGetter 是一个接口，用于获取国家列表，向外暴露给用户
    class CountriesGetter {
        + GetCountries(type string) []string
    }
    <<interface>> CountriesGetter
    CountriesGetter <|.. ProxyManager: implements
%% ProxiesGetter 是一个接口，用于获取代理节点
    class ProxiesGetter {
        + GetProxies(type string, countryCode string, num int) []Proxy
    }
    <<interface>> ProxiesGetter
    ProxiesGetter <|.. ProxyManager: implements

%% ProxyManager 用于管理代理节点
    class ProxyManager {
        - string deviceCode
        - string secret
        - string token
        - ProxyPool pool
        + GetCountries(type string) []string
        + GetProxies(type string, countryCode string, num int) []Proxy
    %% 订阅云端代理池
        - subscribe()
    %% 对代理池中的节点进行测速
        - delay()
    }
    ProxyManager --o ProxyPool: contains


%% ProxyPool 是一个代理池，用于存储代理节点
%% ProxyPool 是否应该有一个持久化存储，否则每次重启需要从云端拉取数据
%% 持久化存储会需要额外配置
    class ProxyPool {
        - map[string]Proxy proxies
        - lock sync.RWMutex
    }
    ProxyPool --o Proxy: contains

    class Proxy {
    }
    <<interface>> Proxy
    Proxy o-- ProxyInfo
    Proxy o-- ProxyConnector
    class ProxyInfo {
        + ID() int
        + Name() string
        + Type() string
        + Load() int
    }
    <<interface>> ProxyInfo

    class ProxyConnector {
        + Delay() int
        + DialConn(conn net.Conn, network string, address string) net.Conn, error
        + DelayTest(url string) int
    }
    <<interface>> ProxyConnector

    class ShadowSocks {
    }
    ProxyConnector <|-- ShadowSocks: implements


```
