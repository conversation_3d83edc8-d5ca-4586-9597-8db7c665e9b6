# wuzhen-gateway

基于`clash`的`gateway`

# 实现方案

基于`clash meta`开发，支持多用户多链路，热加载不会断开已有链接。

## 前置代理

主要以机场提供到`ss`代理和我们自建的`ss`为主，所有出口都会先走机场的节点作为前置代理
所有机场的节点会被加入到一个`proxy_auto`且`type`为`url-test`

```yaml
- name: "pass_gfw"
  type: url-test
  proxies:
    - hk-1
    - hk-2
  url: 'https://www.gstatic.com/generate_204'
```

## 节点列表

在网关展示的节点都是由我们提供的节点列表，
每个节点都会带上`dialer-proxy: pass_gfw`走机场作为前置

## 透明代理

通过`dhcp`服务器分配地址，指定当前路由ip为网关`Tun`模式接管所有流量，走`clash`
默认流量会走一个自动节点
`MATCH,auto`

## 客户端-分流

我们以每个dhcp分配的ip为一个客户端，默认客户端走的是全局的接口点作为出口
每个客户端可以配置自己的出口节点，通过添加一个路由规则让每个客户端走自己的出口
例如：
`SRC-IP-CIDR,*************/32,client-201`

## 多跳实现

用户可以在网关中配置一个多跳链路，从节点列表顺序选取几个节点作为路径。
会根据链路生成新的链路每跳节点并指定`dialer-proxy`为上一跳，第一跳则为`pass_gfw`
```yaml
proxies:
  - name: "自定义路径"
    type: ss
    dialer-proxy: path_自定义路径_2
  - name: "path_自定义路径_2"
    type: ss
    dialer-proxy: path_自定义路径_1
  - name: "path_自定义路径_1"
    type: ss
    dialer-proxy: pass_gfw

rules:
  SRC-IP-CIDR,*************/32,自定义路径
```

## 配置变更

当用户调用了一些接口修改了配置时，会先同步到数据库，再根据数据重新渲染配置文件完成后调用`clash`重载配置用户修改生效
