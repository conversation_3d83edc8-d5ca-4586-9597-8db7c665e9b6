# If you prefer the allow list template instead of the deny list, see community template:
# https://github.com/github/gitignore/blob/main/community/Golang/Go.AllowList.gitignore
#
# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
go.work
/.idea/

# dev file
config.json
config.yaml
/build
/web/content
/scripts/.env
*.db
*.sock
*.back.json
/test/*.json
clash.yaml
server.crt
server.key
.pass
debug
web/res/xproxy.tar.gz
