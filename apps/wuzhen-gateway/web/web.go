package web

import (
	"compress/gzip"
	"embed"
	"errors"
	"fmt"
	"gateway/internal/api"
	"gateway/internal/config"
	_interface "gateway/internal/interface"
	"gateway/internal/log"
	"github.com/gorilla/mux"
	"io"
	"io/fs"
	golog "log"
	"net"
	"net/http"
	"net/http/pprof"
	"os"
	"strings"
)

//go:embed content/*
var content embed.FS

type Server struct {
	router       *mux.Router
	pprofServer  *http.Server
	pprofEnabled bool
}

func NewServer() *Server {
	router := mux.NewRouter()
	return &Server{
		router: router,
	}
}

func (s *Server) SetPprof(enable bool) {
	if enable != s.pprofEnabled {
		switch enable {
		case true:
			s.enablePprof()
		case false:
			s.disablePprof()
		}
	}
}

func (s *Server) enablePprof() {
	s.pprofEnabled = true
	httpMux := http.NewServeMux()
	httpMux.HandleFunc("/debug/pprof/", pprof.Index)
	httpMux.HandleFunc("/debug/pprof/cmdline", pprof.Cmdline)
	httpMux.HandleFunc("/debug/pprof/profile", pprof.Profile)
	httpMux.HandleFunc("/debug/pprof/symbol", pprof.Symbol)
	httpMux.HandleFunc("/debug/pprof/trace", pprof.Trace)
	server := &http.Server{
		Addr:    ":6060",
		Handler: httpMux,
	}
	go func() {
		if server != nil {
			if err := server.ListenAndServe(); err != nil && !errors.Is(err, http.ErrServerClosed) {
				log.GetLogger().Errorf("Start pprof web error: %s", err.Error())
				s.pprofEnabled = false
				s.pprofServer = nil
			}
		}
	}()
	s.pprofServer = server
}

func (s *Server) disablePprof() {
	s.pprofEnabled = false
	if s.pprofServer != nil {
		if err := s.pprofServer.Shutdown(nil); err != nil {
			log.GetLogger().Errorf("Stop pprof web error: %s", err.Error())
		}
		s.pprofServer = nil
	}
}

func (s *Server) Serve() error {
	sub, err := fs.Sub(content, "content")
	if err != nil {
		return err
	}
	s.router.Use(errHandler)
	s.router.Use(gzipHandler)
	s.router.Use(func(handler http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			handler.ServeHTTP(w, r)
		})
	})

	if config.Config().Pprof {
		s.enablePprof()
	}
	api.Register(s.router)
	fileServer := http.FileServer(http.Dir("web"))
	s.router.PathPrefix("/").Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 设置缓存头
		w.Header().Set("Cache-Control", "public, max-age=3600") // 缓存1小时
		path := r.URL.Path
		if path == "" || path == "/" {
			path = "/index.html"
		}
		if info, err2 := os.Stat("web" + path); err2 == nil && !info.IsDir() {
			fileServer.ServeHTTP(w, r)
			return
		}
		_, err2 := sub.Open(path[1:])
		if err2 != nil && strings.HasSuffix(path[1:], "/") {
			_, err2 = sub.Open(fmt.Sprintf("%sindex.html", path[1:]))
		}
		if err2 != nil {
			file, err3 := sub.Open("index.html")
			if err3 == nil {
				w.Header().Set("Content-Type", "text/html; charset=utf-8")
				_, _ = io.Copy(w, file)
				return
			}
		}
		http.FileServer(http.FS(sub)).ServeHTTP(w, r)
	}))
	// 启动 HTTP 服务器，监听 80 端口
	logFile, _ := os.OpenFile(os.DevNull, os.O_WRONLY|os.O_CREATE, 0666)
	server := http.Server{
		Addr:     "0.0.0.0:3000",
		Handler:  s.router,
		ErrorLog: golog.New(logFile, "", 0),
	}

	err = server.ListenAndServe()
	if err != nil {
		log.GetLogger().Errorf("Start https web error: %s", err.Error())
		return err
	}
	return nil
}

// gzipHandler 中间件，用于启用Gzip压缩
func gzipHandler(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 检查客户端是否支持Gzip
		if !strings.Contains(r.Header.Get("Accept-Encoding"), "gzip") {
			next.ServeHTTP(w, r) // 不支持Gzip，直接处理请求
			return
		}
		// 如果是ws连接，直接返回
		if strings.Contains(r.Header.Get("Connection"), "Upgrade") {
			next.ServeHTTP(w, r)
			return
		}
		// 设置响应头，指示内容已被Gzip压缩
		w.Header().Set("Content-Encoding", "gzip")
		// 创建Gzip压缩的ResponseWriter
		gz := gzip.NewWriter(w)
		defer func() { _ = gz.Close() }()
		// 创建自定义的ResponseWriter
		_gzipResponseWriter := &gzipResponseWriter{Writer: gz, ResponseWriter: w}
		// 处理请求
		next.ServeHTTP(_gzipResponseWriter, r)
	})
}

// gzipResponseWriter 自定义的ResponseWriter
type gzipResponseWriter struct {
	io.Writer
	http.ResponseWriter
}

// 重写Write方法以支持Gzip压缩
func (w *gzipResponseWriter) Write(b []byte) (int, error) {
	return w.Writer.Write(b)
}
func errHandler(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		ip := net.ParseIP(strings.Split(r.Host, ":")[0])
		if ip == nil { //存在域名
			if r.Host != config.Config().Domain {
				w.Header().Set("Cache-Control", "no-store, no-cache, must-revalidate, max-age=0")
				w.Header().Set("Pragma", "no-cache")
				w.Header().Set("Expires", "0")
				if _interface.StatusApi.GetNetUptime() <= 0 {
					http.Redirect(
						w,
						r,
						fmt.Sprintf("https://%s/error?msg=wan口网络异常", config.Config().Domain),
						http.StatusFound,
					)
				} else {
					http.Redirect(w, r, fmt.Sprintf("https://%s/error?msg=节点网络异常", config.Config().Domain), http.StatusFound)
				}
				return
			}
		}
		// 处理请求
		next.ServeHTTP(w, r)
	})
}
