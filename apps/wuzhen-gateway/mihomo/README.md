# 改动
##  根据来源对dns分流

搜索关键字 "RemoteAddr" 可看到这部分修改
files:

- `mihomo/tunnel/dns_dialer.go`
- `mihomo/dns/resolver.go`
- `mihomo/dns/server.go`

## 日志

增加了自定义日志等级输出
commit: 4ee9c922bd5520952d593ad6744f9e5dd035f847
files:

- `mihomo/log/log.go`

## api服务有时候未启动的bug

commit: 83ea40511825003f71413d322415392285b9eb1f
files:

- `mihomo/hub/route/server.go`

## 断网 dns 错误劫持

commit: a757d4a9e626db949d35a87e77b8e24d109835ba

files:
 - `mihomo/dns/client.go`

## 添加日志堆栈信息

- `mihomo/log/log.go`
commit: 38c03fdf1a52992eb06783f931029345dad618c7

## 添加根据src均衡负载
- `mihomo/adapter/outboundgroup/loadbalance.go`
commit:7086a0c926cf6afe6665758d7776e998f986e427
