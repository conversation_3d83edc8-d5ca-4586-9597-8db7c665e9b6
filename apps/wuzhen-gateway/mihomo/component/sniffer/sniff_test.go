package sniffer

import (
	"bytes"
	"encoding/hex"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestQuicHeaders(t *testing.T) {
	cases := []struct {
		input  string
		domain string
	}{
		{
			input:  "cd0000000108f1fb7bcc78aa5e7203a8f86400421531fe825b19541876db6c55c38890cd73149d267a084afee6087304095417a3033df6a81bbb71d8512e7a3e16df1e277cae5df3182cb214b8fe982ba3fdffbaa9ffec474547d55945f0fddbeadfb0b5243890b2fa3da45169e2bd34ec04b2e29382f48d612b28432a559757504d158e9e505407a77dd34f4b60b8d3b555ee85aacd6648686802f4de25e7216b19e54c5f78e8a5963380c742d861306db4c16e4f7fc94957aa50b9578a0b61f1e406b2ad5f0cd3cd271c4d99476409797b0c3cb3efec256118912d4b7e4fd79d9cb9016b6e5eaa4f5e57b637b217755daf8968a4092bed0ed5413f5d04904b3a61e4064f9211b2629e5b52a89c7b19f37a713e41e27743ea6dfa736dfa1bb0a4b2bc8c8dc632c6ce963493a20c550e6fdb2475213665e9a85cfc394da9cec0cf41f0c8abed3fc83be5245b2b5aa5e825d29349f721d30774ef5bf965b540f3d8d98febe20956b1fc8fa047e10e7d2f921c9c6622389e02322e80621a1cf5264e245b7276966eb02932584e3f7038bd36aa908766ad3fb98344025dec18670d6db43a1c5daac00937fce7b7c7d61ff4e6efd01a2bdee0ee183108b926393df4f3d74bbcbb015f240e7e346b7d01c41111a401225ce3b095ab4623a5836169bf9599eeca79d1d2e9b2202b5960a09211e978058d6fc0484eff3e91ce4649a5e3ba15b906d334cf66e28d9ff575406e1ae1ac2febafd72870b6f5d58fc5fb949cb1f40feb7c1d9ce5e71b",
			domain: "www.google.com",
		},
		{
			input:  "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",
			domain: "cloudflare-dns.com",
		},
	}
	q, err := NewQuicSniffer(SnifferConfig{})
	assert.NoError(t, err)

	for _, test := range cases {
		pkt, err := hex.DecodeString(test.input)
		assert.NoError(t, err)
		oriPkt := bytes.Clone(pkt)
		domain, err := q.SniffData(pkt)
		assert.NoError(t, err)
		assert.Equal(t, test.domain, domain)
		assert.Equal(t, oriPkt, pkt) // ensure input data not changed
	}
}

func TestTLSHeaders(t *testing.T) {
	cases := []struct {
		input  []byte
		domain string
		err    bool
	}{
		{
			input: []byte{
				0x16, 0x03, 0x01, 0x00, 0xc8, 0x01, 0x00, 0x00,
				0xc4, 0x03, 0x03, 0x1a, 0xac, 0xb2, 0xa8, 0xfe,
				0xb4, 0x96, 0x04, 0x5b, 0xca, 0xf7, 0xc1, 0xf4,
				0x2e, 0x53, 0x24, 0x6e, 0x34, 0x0c, 0x58, 0x36,
				0x71, 0x97, 0x59, 0xe9, 0x41, 0x66, 0xe2, 0x43,
				0xa0, 0x13, 0xb6, 0x00, 0x00, 0x20, 0x1a, 0x1a,
				0xc0, 0x2b, 0xc0, 0x2f, 0xc0, 0x2c, 0xc0, 0x30,
				0xcc, 0xa9, 0xcc, 0xa8, 0xcc, 0x14, 0xcc, 0x13,
				0xc0, 0x13, 0xc0, 0x14, 0x00, 0x9c, 0x00, 0x9d,
				0x00, 0x2f, 0x00, 0x35, 0x00, 0x0a, 0x01, 0x00,
				0x00, 0x7b, 0xba, 0xba, 0x00, 0x00, 0xff, 0x01,
				0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x16, 0x00,
				0x14, 0x00, 0x00, 0x11, 0x63, 0x2e, 0x73, 0x2d,
				0x6d, 0x69, 0x63, 0x72, 0x6f, 0x73, 0x6f, 0x66,
				0x74, 0x2e, 0x63, 0x6f, 0x6d, 0x00, 0x17, 0x00,
				0x00, 0x00, 0x23, 0x00, 0x00, 0x00, 0x0d, 0x00,
				0x14, 0x00, 0x12, 0x04, 0x03, 0x08, 0x04, 0x04,
				0x01, 0x05, 0x03, 0x08, 0x05, 0x05, 0x01, 0x08,
				0x06, 0x06, 0x01, 0x02, 0x01, 0x00, 0x05, 0x00,
				0x05, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x12,
				0x00, 0x00, 0x00, 0x10, 0x00, 0x0e, 0x00, 0x0c,
				0x02, 0x68, 0x32, 0x08, 0x68, 0x74, 0x74, 0x70,
				0x2f, 0x31, 0x2e, 0x31, 0x00, 0x0b, 0x00, 0x02,
				0x01, 0x00, 0x00, 0x0a, 0x00, 0x0a, 0x00, 0x08,
				0xaa, 0xaa, 0x00, 0x1d, 0x00, 0x17, 0x00, 0x18,
				0xaa, 0xaa, 0x00, 0x01, 0x00,
			},
			domain: "c.s-microsoft.com",
			err:    false,
		},
		{
			input: []byte{
				0x16, 0x03, 0x01, 0x00, 0xee, 0x01, 0x00, 0x00,
				0xea, 0x03, 0x03, 0xe7, 0x91, 0x9e, 0x93, 0xca,
				0x78, 0x1b, 0x3c, 0xe0, 0x65, 0x25, 0x58, 0xb5,
				0x93, 0xe1, 0x0f, 0x85, 0xec, 0x9a, 0x66, 0x8e,
				0x61, 0x82, 0x88, 0xc8, 0xfc, 0xae, 0x1e, 0xca,
				0xd7, 0xa5, 0x63, 0x20, 0xbd, 0x1c, 0x00, 0x00,
				0x8b, 0xee, 0x09, 0xe3, 0x47, 0x6a, 0x0e, 0x74,
				0xb0, 0xbc, 0xa3, 0x02, 0xa7, 0x35, 0xe8, 0x85,
				0x70, 0x7c, 0x7a, 0xf0, 0x00, 0xdf, 0x4a, 0xea,
				0x87, 0x01, 0x14, 0x91, 0x00, 0x20, 0xea, 0xea,
				0xc0, 0x2b, 0xc0, 0x2f, 0xc0, 0x2c, 0xc0, 0x30,
				0xcc, 0xa9, 0xcc, 0xa8, 0xcc, 0x14, 0xcc, 0x13,
				0xc0, 0x13, 0xc0, 0x14, 0x00, 0x9c, 0x00, 0x9d,
				0x00, 0x2f, 0x00, 0x35, 0x00, 0x0a, 0x01, 0x00,
				0x00, 0x81, 0x9a, 0x9a, 0x00, 0x00, 0xff, 0x01,
				0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x18, 0x00,
				0x16, 0x00, 0x00, 0x13, 0x77, 0x77, 0x77, 0x30,
				0x37, 0x2e, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x74,
				0x61, 0x6c, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x00,
				0x17, 0x00, 0x00, 0x00, 0x23, 0x00, 0x00, 0x00,
				0x0d, 0x00, 0x14, 0x00, 0x12, 0x04, 0x03, 0x08,
				0x04, 0x04, 0x01, 0x05, 0x03, 0x08, 0x05, 0x05,
				0x01, 0x08, 0x06, 0x06, 0x01, 0x02, 0x01, 0x00,
				0x05, 0x00, 0x05, 0x01, 0x00, 0x00, 0x00, 0x00,
				0x00, 0x12, 0x00, 0x00, 0x00, 0x10, 0x00, 0x0e,
				0x00, 0x0c, 0x02, 0x68, 0x32, 0x08, 0x68, 0x74,
				0x74, 0x70, 0x2f, 0x31, 0x2e, 0x31, 0x75, 0x50,
				0x00, 0x00, 0x00, 0x0b, 0x00, 0x02, 0x01, 0x00,
				0x00, 0x0a, 0x00, 0x0a, 0x00, 0x08, 0x9a, 0x9a,
				0x00, 0x1d, 0x00, 0x17, 0x00, 0x18, 0x8a, 0x8a,
				0x00, 0x01, 0x00,
			},
			domain: "www07.clicktale.net",
			err:    false,
		},
		{
			input: []byte{
				0x16, 0x03, 0x01, 0x00, 0xe6, 0x01, 0x00, 0x00, 0xe2, 0x03, 0x03, 0x81, 0x47, 0xc1,
				0x66, 0xd5, 0x1b, 0xfa, 0x4b, 0xb5, 0xe0, 0x2a, 0xe1, 0xa7, 0x87, 0x13, 0x1d, 0x11, 0xaa, 0xc6,
				0xce, 0xfc, 0x7f, 0xab, 0x94, 0xc8, 0x62, 0xad, 0xc8, 0xab, 0x0c, 0xdd, 0xcb, 0x20, 0x6f, 0x9d,
				0x07, 0xf1, 0x95, 0x3e, 0x99, 0xd8, 0xf3, 0x6d, 0x97, 0xee, 0x19, 0x0b, 0x06, 0x1b, 0xf4, 0x84,
				0x0b, 0xb6, 0x8f, 0xcc, 0xde, 0xe2, 0xd0, 0x2d, 0x6b, 0x0c, 0x1f, 0x52, 0x53, 0x13, 0x00, 0x08,
				0x13, 0x02, 0x13, 0x03, 0x13, 0x01, 0x00, 0xff, 0x01, 0x00, 0x00, 0x91, 0x00, 0x00, 0x00, 0x0c,
				0x00, 0x0a, 0x00, 0x00, 0x07, 0x64, 0x6f, 0x67, 0x66, 0x69, 0x73, 0x68, 0x00, 0x0b, 0x00, 0x04,
				0x03, 0x00, 0x01, 0x02, 0x00, 0x0a, 0x00, 0x0c, 0x00, 0x0a, 0x00, 0x1d, 0x00, 0x17, 0x00, 0x1e,
				0x00, 0x19, 0x00, 0x18, 0x00, 0x23, 0x00, 0x00, 0x00, 0x16, 0x00, 0x00, 0x00, 0x17, 0x00, 0x00,
				0x00, 0x0d, 0x00, 0x1e, 0x00, 0x1c, 0x04, 0x03, 0x05, 0x03, 0x06, 0x03, 0x08, 0x07, 0x08, 0x08,
				0x08, 0x09, 0x08, 0x0a, 0x08, 0x0b, 0x08, 0x04, 0x08, 0x05, 0x08, 0x06, 0x04, 0x01, 0x05, 0x01,
				0x06, 0x01, 0x00, 0x2b, 0x00, 0x07, 0x06, 0x7f, 0x1c, 0x7f, 0x1b, 0x7f, 0x1a, 0x00, 0x2d, 0x00,
				0x02, 0x01, 0x01, 0x00, 0x33, 0x00, 0x26, 0x00, 0x24, 0x00, 0x1d, 0x00, 0x20, 0x2f, 0x35, 0x0c,
				0xb6, 0x90, 0x0a, 0xb7, 0xd5, 0xc4, 0x1b, 0x2f, 0x60, 0xaa, 0x56, 0x7b, 0x3f, 0x71, 0xc8, 0x01,
				0x7e, 0x86, 0xd3, 0xb7, 0x0c, 0x29, 0x1a, 0x9e, 0x5b, 0x38, 0x3f, 0x01, 0x72,
			},
			domain: "dogfish",
			err:    false,
		},
		{
			input: []byte{
				0x16, 0x03, 0x01, 0x01, 0x03, 0x01, 0x00, 0x00,
				0xff, 0x03, 0x03, 0x3d, 0x89, 0x52, 0x9e, 0xee,
				0xbe, 0x17, 0x63, 0x75, 0xef, 0x29, 0xbd, 0x14,
				0x6a, 0x49, 0xe0, 0x2c, 0x37, 0x57, 0x71, 0x62,
				0x82, 0x44, 0x94, 0x8f, 0x6e, 0x94, 0x08, 0x45,
				0x7f, 0xdb, 0xc1, 0x00, 0x00, 0x3e, 0xc0, 0x2c,
				0xc0, 0x30, 0x00, 0x9f, 0xcc, 0xa9, 0xcc, 0xa8,
				0xcc, 0xaa, 0xc0, 0x2b, 0xc0, 0x2f, 0x00, 0x9e,
				0xc0, 0x24, 0xc0, 0x28, 0x00, 0x6b, 0xc0, 0x23,
				0xc0, 0x27, 0x00, 0x67, 0xc0, 0x0a, 0xc0, 0x14,
				0x00, 0x39, 0xc0, 0x09, 0xc0, 0x13, 0x00, 0x33,
				0x00, 0x9d, 0x00, 0x9c, 0x13, 0x02, 0x13, 0x03,
				0x13, 0x01, 0x00, 0x3d, 0x00, 0x3c, 0x00, 0x35,
				0x00, 0x2f, 0x00, 0xff, 0x01, 0x00, 0x00, 0x98,
				0x00, 0x00, 0x00, 0x10, 0x00, 0x0e, 0x00, 0x00,
				0x0b, 0x31, 0x30, 0x2e, 0x34, 0x32, 0x2e, 0x30,
				0x2e, 0x32, 0x34, 0x33, 0x00, 0x0b, 0x00, 0x04,
				0x03, 0x00, 0x01, 0x02, 0x00, 0x0a, 0x00, 0x0a,
				0x00, 0x08, 0x00, 0x1d, 0x00, 0x17, 0x00, 0x19,
				0x00, 0x18, 0x00, 0x23, 0x00, 0x00, 0x00, 0x0d,
				0x00, 0x20, 0x00, 0x1e, 0x04, 0x03, 0x05, 0x03,
				0x06, 0x03, 0x08, 0x04, 0x08, 0x05, 0x08, 0x06,
				0x04, 0x01, 0x05, 0x01, 0x06, 0x01, 0x02, 0x03,
				0x02, 0x01, 0x02, 0x02, 0x04, 0x02, 0x05, 0x02,
				0x06, 0x02, 0x00, 0x16, 0x00, 0x00, 0x00, 0x17,
				0x00, 0x00, 0x00, 0x2b, 0x00, 0x09, 0x08, 0x7f,
				0x14, 0x03, 0x03, 0x03, 0x02, 0x03, 0x01, 0x00,
				0x2d, 0x00, 0x03, 0x02, 0x01, 0x00, 0x00, 0x28,
				0x00, 0x26, 0x00, 0x24, 0x00, 0x1d, 0x00, 0x20,
				0x13, 0x7c, 0x6e, 0x97, 0xc4, 0xfd, 0x09, 0x2e,
				0x70, 0x2f, 0x73, 0x5a, 0x9b, 0x57, 0x4d, 0x5f,
				0x2b, 0x73, 0x2c, 0xa5, 0x4a, 0x98, 0x40, 0x3d,
				0x75, 0x6e, 0xb4, 0x76, 0xf9, 0x48, 0x8f, 0x36,
			},
			domain: "10.42.0.243",
			err:    false,
		},
	}

	for _, test := range cases {
		input := bytes.Clone(test.input)
		domain, err := SniffTLS(test.input)
		if test.err {
			if err == nil {
				t.Errorf("Exepct error but nil in test %v", test)
			}
		} else {
			if err != nil {
				t.Errorf("Expect no error but actually %s in test %v", err.Error(), test)
			}
			if *domain != test.domain {
				t.Error("expect domain ", test.domain, " but got ", domain)
			}
		}
		assert.Equal(t, input, test.input)
	}
}
