syntax = "proto3";

package mihomo.component.geodata.router;
option csharp_namespace = "Mihomo.Component.Geodata.Router";
option go_package = "github.com/metacubex/mihomo/component/geodata/router";
option java_package = "com.mihomo.component.geodata.router";
option java_multiple_files = true;

// Domain for routing decision.
message Domain {
  // Type of domain value.
  enum Type {
    // The value is used as is.
    Plain = 0;
    // The value is used as a regular expression.
    Regex = 1;
    // The value is a root domain.
    Domain = 2;
    // The value is a domain.
    Full = 3;
  }

  // Domain matching type.
  Type type = 1;

  // Domain value.
  string value = 2;

  message Attribute {
    string key = 1;

    oneof typed_value {
      bool bool_value = 2;
      int64 int_value = 3;
    }
  }

  // Attributes of this domain. May be used for filtering.
  repeated Attribute attribute = 3;
}

// IP for routing decision, in CIDR form.
message CIDR {
  // IP address, should be either 4 or 16 bytes.
  bytes ip = 1;

  // Number of leading ones in the network mask.
  uint32 prefix = 2;
}

message GeoIP {
  string country_code = 1;
  repeated CIDR cidr = 2;
  bool reverse_match = 3;
}

message GeoIPList {
  repeated GeoIP entry = 1;
}

message GeoSite {
  string country_code = 1;
  repeated Domain domain = 2;
}

message GeoSiteList {
  repeated GeoSite entry = 1;
}
