{"inbounds": [{"port": 10002, "listen": "0.0.0.0", "protocol": "trojan", "settings": {"clients": [{"password": "example", "email": "<EMAIL>", "flow": "xtls-rprx-direct", "level": 0}]}, "streamSettings": {"network": "tcp", "security": "xtls", "xtlsSettings": {"certificates": [{"certificateFile": "/etc/ssl/v2ray/fullchain.pem", "keyFile": "/etc/ssl/v2ray/privkey.pem"}]}}}], "outbounds": [{"protocol": "freedom"}], "log": {"loglevel": "debug"}}