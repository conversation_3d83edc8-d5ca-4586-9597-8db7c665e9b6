package log

import (
	"fmt"
	"os"
	"runtime"

	"github.com/metacubex/mihomo/common/observable"

	log "github.com/sirupsen/logrus"
)

var (
	logCh  = make(chan Event)
	source = observable.NewObservable[Event](logCh)
	level  = INFO
)

func init() {
	log.SetOutput(os.Stdout)
	log.SetLevel(log.DebugLevel)
	log.SetFormatter(&log.TextFormatter{
		FullTimestamp:             true,
		TimestampFormat:           "2006-01-02T15:04:05.999999999Z07:00",
		EnvironmentOverrideColors: true,
	})
}

type Event struct {
	LogLevel     LogLevel
	Payload      string
	AlwaysOutput bool
	Caller       string
}

func (e *Event) Type() string {
	return e.LogLevel.String()
}

func Infoln(format string, v ...any) {
	event := newLog(false, INFO, format, v...)
	logCh <- event
	print(event)
}

func Warnln(format string, v ...any) {
	event := newLog(false, WARNING, format, v...)
	logCh <- event
	print(event)
}

func Errorln(format string, v ...any) {
	event := newLog(false, ERROR, format, v...)
	logCh <- event
	print(event)
}

func Debugln(format string, v ...any) {
	event := newLog(false, DEBUG, format, v...)
	logCh <- event
	print(event)
}

func Fatalln(format string, v ...any) {
	log.Fatalf(format, v...)
}
func Println(logLevel LogLevel, format string, v ...any) {
	event := newLog(true, logLevel, format, v...)
	_, file, line, ok := runtime.Caller(2)
	if ok {
		event.Caller = fmt.Sprintf("%s:%d", file, line)
	}
	logCh <- event
	print(event)
}
func Subscribe() observable.Subscription[Event] {
	sub, _ := source.Subscribe()
	return sub
}

func UnSubscribe(sub observable.Subscription[Event]) {
	source.UnSubscribe(sub)
}

func Level() LogLevel {
	return level
}

func SetLevel(newLevel LogLevel) {
	level = newLevel
}

func print(data Event) {
	if data.LogLevel < level && !data.AlwaysOutput {
		return
	}

	msg := data.Payload
	if data.Caller != "" {
		msg = fmt.Sprintf("[%s] %s", data.Caller, msg)
	}

	switch data.LogLevel {
	case INFO:
		log.Infoln(msg)
	case WARNING:
		log.Warnln(msg)
	case ERROR:
		log.Errorln(msg)
	case DEBUG:
		log.Debugln(msg)
	}
}

func newLog(alwaysOutput bool, logLevel LogLevel, format string, v ...any) Event {
	event := Event{
		AlwaysOutput: alwaysOutput,
		LogLevel:     logLevel,
		Payload:      fmt.Sprintf(format, v...),
	}
	_, file, line, ok := runtime.Caller(2)
	if ok {
		event.Caller = fmt.Sprintf("%s:%d", file, line)
	}
	return event
}
