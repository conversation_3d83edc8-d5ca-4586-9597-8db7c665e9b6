package main

import (
	"flag"
	"fmt"
	"net"
	"time"

	"github.com/metacubex/mihomo/common/atomic"
)

var client string
var begin int64
var end int64
var count atomic.Int64

func main() {
	flag.StringVar(&client, "c", "", "client mode, addr")
	flag.Int64Var(&begin, "b", 20000, "begin")
	flag.Int64Var(&end, "e", 20500, "end")
	flag.Parse()
	if end < begin {
		fmt.Println("end must greater than begin")
		return
	}
	if client == "" {
		//server mode
		for i := begin; i < end; i++ {
			listen, err := net.Listen("tcp", fmt.Sprintf(":%d", i))
			if err != nil {
				fmt.Println(err)
				continue
			}
			fmt.Println("server listen", listen.Addr())
			go server(listen)
		}
	} else {
		//client mode
		for i := begin; i < end; i++ {
			conn, err := net.Dial("tcp", fmt.Sprintf("%s:%d", client, i))
			if err != nil {
				fmt.Println(err)
				continue
			}
			fmt.Println("client dial", conn.RemoteAddr())
			time.Sleep(time.Second / 10)
			go clientHandler(conn)
		}
	}
	for {
		time.Sleep(time.Second)
		fmt.Println("count", count.Load())
	}
}
func server(listener net.Listener) {
	for {
		accept, err := listener.Accept()
		if err != nil {
			return
		}
		fmt.Println("server accept", accept.RemoteAddr())
		go serverHandler(accept)
	}
}
func serverHandler(conn net.Conn) {
	bytes := make([]byte, 256)
	count.Add(1)
	defer count.Add(-1)
	for {
		n, err := conn.Read(bytes)
		if err != nil {
			fmt.Println(err)
			return
		}
		_, err = conn.Write(bytes[:n])
		if err != nil {
			fmt.Println(err)
			return
		}
	}
}
func clientHandler(conn net.Conn) {
	count.Add(1)
	defer count.Add(-1)
	for {
		n, err := conn.Write([]byte(fmt.Sprintf("hello, this is %s", conn.RemoteAddr().String())))
		if err != nil {
			fmt.Println(err)
			return
		}
		_, err = conn.Read(make([]byte, n))
		if err != nil {
			fmt.Println(err)
			return
		}
		time.Sleep(time.Second)
	}
}
