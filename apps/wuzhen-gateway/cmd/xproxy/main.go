package main

import (
	"flag"
	"fmt"
	"os"
	"os/signal"
	"syscall"

	clashconfig "github.com/metacubex/mihomo/config"
	"github.com/metacubex/mihomo/constant"
	"github.com/metacubex/mihomo/hub/executor"
	"github.com/metacubex/mihomo/log"
)

var port int64
var password string

func main() {
	flag.Int64Var(&port, "p", 17443, "ss port")
	flag.StringVar(&password, "k", "ss-password", "ss password")
	flag.Parse()
	log.SetLevel(log.SILENT)
	dir, err := os.Getwd()
	if err != nil {
		fmt.Printf("error: %v\n", err)
		return
	}
	constant.SetHomeDir(dir)
	rawConfig, err := clashconfig.ParseRawConfig(&clashconfig.RawConfig{
		LogLevel: log.SILENT,
		DNS: clashconfig.RawDNS{
			DefaultNameserver: []string{
				"*********",
			},
		},
		Listeners: []map[string]any{
			{
				"name":     "ss",
				"type":     "shadowsocks",
				"listen":   "0.0.0.0",
				"port":     port,
				"cipher":   "aes-256-gcm",
				"password": password,
				"udp":      true,
			},
		},
	})
	if err != nil {
		fmt.Printf("error: %v\n", err)
		return
	}
	executor.ApplyConfig(rawConfig, true)
	defer executor.Shutdown()
	fmt.Printf("Listening on %d\n", port)
	termSign := make(chan os.Signal, 1)
	signal.Notify(termSign, syscall.SIGINT, syscall.SIGTERM)
	for {
		select {
		case <-termSign:
			fmt.Println("Shutting down...")
			return
		}
	}
}
