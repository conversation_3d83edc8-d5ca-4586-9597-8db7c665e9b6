```mermaid
---
title: Wuzhen V2
---
classDiagram
    class Config {
        + string DeviceCode
        + string Secret
        + string CloudGateway
        + string Domain
        + bool DisablePassGFW
        + DHCPConfig DHCP
        + []Sting Flags
        + LoadConfig(string) Config
        + GetConfig() Config
    }

    class DHCPConfig {
        + bool Enable
        + int Lease
        + string CIDR
        + string IfName
    }
    Config o-- DHCPConfig: contain

    class DB {
        - gorm.DB db
        + Init() error
        + GetDB() gorm.DB
    }

    class Service {
        + Run() Error
        + Stop() Error
    }
    <<interface>> Service

    class Reloadable {
        + Reload()
    }
    <<interface>> Reloadable
    Reloadable <|-- Service: extends

    class DHCPServer {
        - dhcp.Server server
        - cidr.CIDR cidr
        - net.IP begin
        - net.IP end
        + New() DHCPServer
    }
    Service <|.. DHCPServer: implement

    class DNSServer {
        - []string nameserver
        - []string defaultNameserver
        - []string proxyServerNameserver
        - []string fallback
        - string listen
        + New() DNSServer
    }
    Service <|.. DNSServer: implement

    class Cron {
        - []Task task
        + New() Cron
    }
    Service <|.. Cron: implement

    class Task {
        - time.Duration interval
        + Run()
        + Getinterval() time.Duration
    }
    <<interface>> Task
    Cron --o Task: contain

    class Traffic
    Task <|.. Traffic: implement
    class Delay
    Task <|.. Delay: implement
    class RemoteUpdate
    Task <|.. RemoteUpdate: implement
    class Monitor
    Task <|.. Monitor: implement
    class WebServer {
        - mux.Router router
        - bool enablePprof
        + New() WebServer
    }
    Service <|.. WebServer: implement
    Reloadable <|.. WebServer: implement

    class TUN {
        - []RuleProvider ruleProvider
        + New() Service
    }
    Service <|.. InboundManager: implement
    Reloadable <|.. InboundManager: implement
    InboundManager --o RuleProvider: contain

    class RuleProvider {
        + Match(Meta) bool
        + SetDefault(Circuit)
        + SetRule(Rule)
        + GetCircuit(Meta) Circuit
    }
    <<interface>> RuleProvider
    RuleProvider --o Circuit: contain

    class RouteRuleProvider {
    }
    RuleProvider <|.. RouteRuleProvider: implement

    class DropRuleProvider {
    }
    RuleProvider <|.. DropRuleProvider: implement

    class Rule {
        + Match(Meta) Circuit
    }
    <<interface>> Rule
    RuleProvider --o Rule: contain

    class JumpChangeRule {
        - net.IP sourceIP
        - time.Duration interval
        - []string countries
        - New(net.IP, []string, time.Duration) Rule, error
    }
    Rule <|.. JumpChangeRule: implement

    class MatchSourceIPRule {
        - net.IP sourceIP
        - Circuit circuit
        - New(net.IP, Circuit) Rule, error
    }
    Rule <|.. MatchSourceIPRule: implement

    class Proxy {
    }
    <<interface>> Proxy

    class ProxyIdentifier {
        + ID() int
        + Name() string
        + Type() string
    }
    <<interface>> ProxyIdentifier
    ProxyIdentifier --o Proxy: aggregation

    class ProxyConnector {
        + ConnectInfo() string
        + DialConn(conn net.Conn, network string, address string) net.Conn, error
    }
    <<interface>> ProxyConnector
    ProxyConnector --o Proxy: aggregation

    class ProxyTester {
        + Delay(url string) int
    }
    <<interface>> ProxyTester
    ProxyTester --o Proxy: aggregation

    class ShadowSocks {
    }
    Proxy <|.. ShadowSocks: implement

    class Circuit {
        - []Proxy proxies
        - string name
        + Dial(network string, address string)(net.Conn, error)
        + Delay(url string) int
    }
    Circuit --o Proxy: contain

    class CircuitBuilder {
        + New() CircuitBuilder
        + Append(Proxy)
        + Build() Circuit, error
    }
    CircuitBuilder --o Circuit: build
```
