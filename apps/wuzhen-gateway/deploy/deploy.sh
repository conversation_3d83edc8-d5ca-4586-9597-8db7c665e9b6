#!/bin/bash
#set -eux
# 检查是否是 root 用户
if [ "$EUID" -ne 0 ]; then
    echo "请用 root用户 运行"
    exit 1
fi
# 获取系统架构
ARCH=$(uname -m)
BIN_FILE="gateway_arm64"
# 判断架构类型
if [[ "$ARCH" == "x86_64" ]]; then
    BIN_FILE="gateway_amd64"
fi
apt update
apt-mark hold wireless-regdb
apt-mark hold armbian-bsp-cli-odroidn2-current
apt upgrade --yes
apt install -y libqmi-utils uuid-runtime udhcpc curl wget net-tools hostapd wpasupplicant iptables-persistent bridge-utils tcpdump mtr vim wireless-tools
# 生成新的机器ID
new_machine_id=$(uuidgen | tr -d '-')
# 读取当前的机器ID
old_machine_id=$(cat /etc/machine-id)
# 生成新的机器ID
new_machine_id=$(uuidgen | tr -d '-')
# 提示用户确认
echo "当前的机器ID是: $old_machine_id"
echo "新的机器ID将被设置为: $new_machine_id"
echo "克隆的虚拟机机器id一样可能导致mac地址冲突"
read -p "你确定要修改 /etc/machine-id 吗？(yes/no): " response

# 检查用户的响应
if [[ "$response" == "yes" ]]; then
    # 执行命令修改 /etc/machine-id
    sh -c "echo $new_machine_id > /etc/machine-id"
    if [[ $? -eq 0 ]]; then
        echo "机器ID已成功修改为 $new_machine_id"
    else
        echo "修改机器ID时出错"
    fi
else
    echo "操作已取消"
fi

# 获取网络接口列表，过滤出需要的接口并编号
ip link | grep -Pi '^\d+: (eth|ens|enp|eno)\S*:' | awk -F': ' '{print $2}' | nl

# 提示用户选择一个网卡作为 WAN 口
read -p "请选择一个网卡作为 WAN 口 (输入编号): " iface_number

# 获取用户选择的网卡名
WAN_IFACE=$(ip link | grep -Pi '^\d+: (eth|ens|enp|eno)\S*:' | awk -F': ' '{print $2}' | sed -n "${iface_number}p" | xargs)

if [ -z "$WAN_IFACE" ]; then
    echo "无效的选择。"
    exit 1
fi
echo "使用网卡: $WAN_IFACE"
# 删除网络接口配置
rm /etc/network/interfaces
rm -rf /etc/systemd/network/*
# 检测以 wl 开头的 WiFi 网卡
wifi_interface=$(ip link show | grep -E '^[0-9]+: wl')
if [[ -n "$wifi_interface" ]]; then
    echo "检测到 WiFi 网卡,将启用 AP 接入请选择一个网卡"
    ip link | grep -Pi '^\d+: (wl)\S*:' | awk -F': ' '{print $2}' | nl
    STA_IFACE=wlan0
    AP_IFACE=wlan1
    cat <<EOF | tee /etc/hostapd/hostapd.conf
driver=nl80211
# 使用nl80211驱动程序
wpa_passphrase=88888888
# WPA密码
auth_algs=1
# 认证算法，1表示仅WPA-PSK
wpa=2
# WPA版本，2表示WPA2
wpa_pairwise=CCMP
# WPA的加密模式，使用CCMP
ssid=x-net
# SSID名称
bridge=br-lan
# 配置桥接接口
interface=${AP_IFACE}
# 设置无线接口为wlan1
wpa_key_mgmt=WPA-PSK WPA-PSK-SHA256 SAE
# WPA密钥管理方式
country_code=CN
# 设置国家代码为中国
ieee80211d=1
# 启用802.11d，以支持国家法规
ieee80211h=1
# 启用802.11h，以支持动态频谱管理
ieee80211n=1
# 启用802.11n，支持更高的数据速率
ieee80211ac=1
# 启用802.11ac，支持更高的频宽和速率
ieee80211ax=1
# 启用802.11ax，支持更高的网络效率和容量
ieee80211w=1
# 启用802.11w，支持管理帧保护
sae_require_mfp=1
# 强制使用管理帧保护
hw_mode=a
# 硬件模式，'a'表示使用5GHz频段
channel=149
# 无线信道设置为149
ht_capab=[GF][HT40+][HT40-][LDPC][MAX-AMSDU-7935][RX-STBC1][RXLDPC][SHORT-GI-20][SHORT-GI-40][TX-STBC]
# HT（高通量）能力配置
vht_oper_chwidth=1
# VHT操作频宽，1表示80MHz
vht_oper_centr_freq_seg0_idx=155
# VHT中心频段，155对应于5GHz频段
vht_capab=[MAX-A-MPDU-LEN-EXP3][MAX-MPDU-7991][MU-BEAMFORMEE][MU-BEAMFORMER][RX-ANTENNA-PATTERN][RX-STBC-1][RXLDPC][SHORT-GI-80][SU-BEAMFORMEE][SU-BEAMFORMER][TX-ANTENNA-PATTERN][TX-STBC-2BY1]
# VHT能力配置
he_su_beamformee=1
# 启用单用户beamforming能力
he_su_beamformer=1
# 启用多用户beamforming能力
he_mu_beamformer=1
# 启用多用户beamforming能力
he_basic_mcs_nss_set=2
# 基本MCS和NSS设置
he_oper_chwidth=1
# HE操作频宽，1表示80MHz
he_oper_centr_freq_seg0_idx=155
# HE中心频段
disassoc_low_ack=0
# 禁用因低确认而断开连接
preamble=1
# 预导引模式设置
wmm_enabled=1
# 启用WMM（Wi-Fi多媒体）功能
ignore_broadcast_ssid=0
# 不忽略广播SSID
uapsd_advertisement_enabled=1
# 启用U-APSD广告 跟节能有关
EOF

    # sta
    cat <<EOF | tee /etc/wpa_supplicant/wpa_supplicant.conf
ctrl_interface=/var/run/wpa_supplicant
update_config=1
EOF

    cat <<EOF | tee /usr/lib/systemd/system/wpa_supplicant.service
[Unit]
Description=WPA supplicant for wlan0
Before=network.target

[Service]
ExecStart=/usr/sbin/wpa_supplicant -i ${STA_IFACE} -c /etc/wpa_supplicant/wpa_supplicant.conf
Restart=on-failure
RestartSec=3

[Install]
WantedBy=multi-user.target
EOF

    cat <<EOF | tee /etc/systemd/network/20-wlan0.network
[Match]
Name=wlan0

[Network]
DHCP=ipv4

[DHCPv4]
RouteMetric=150
EOF

    systemctl stop wpa_supplicant.service
    systemctl disable wpa_supplicant.service
    systemctl daemon-reload
    systemctl unmask hostapd
    systemctl enable hostapd
    systemctl enable wpa_supplicant
fi

# 创建 br-lan 网桥配置文件
cat <<EOF | tee /etc/systemd/network/10-br-lan.netdev
[NetDev]
Name=br-lan
Kind=bridge
EOF

# 创建 br-wan 网桥配置文件
cat <<EOF | tee /etc/systemd/network/10-br-wan.netdev
[NetDev]
Name=br-wan
Kind=bridge
EOF

# 配置 WAN 接口连接到 br-wan 网桥
cat <<EOF | tee /etc/systemd/network/10-$WAN_IFACE.network
[Match]
Name=$WAN_IFACE

[Network]
Bridge=br-wan
EOF

# 配置 br-wan 网桥的网络参数
cat <<EOF | tee /etc/systemd/network/20-br-wan.network
[Match]
Name=br-wan

[Network]
DHCP=ipv4

[DHCPv4]
RouteMetric=100

EOF
#  配置TUN0
cat <<EOF | tee /etc/systemd/network/20-tun0.network
[Match]
Name=tun0

[Network]
Address=**********/30

#代理表
[Route]
Gateway=0.0.0.0
Table=100
# 黑洞表
[Route]
Gateway=0.0.0.0
Type=blackhole
Table=101
EOF

# 配置 br-lan 网桥的网络参数
cat <<EOF >/etc/systemd/network/30-br-lan.network
[Match]
Name=br-lan

[Network]
Address=**********/20
IPv6AcceptRA=no
DHCPServer=no
ConfigureWithoutCarrier=yes
# 局域网内流量正常走
[RoutingPolicyRule]
From=**********/20
To=**********/20
Table=main
Priority=1000
# tcp流量走 100 表(代理)
[RoutingPolicyRule]
From=**********/20
IPProtocol=tcp
Table=100
Priority=2000
# udp流量走 100 表(代理)
[RoutingPolicyRule]
From=**********/20
IPProtocol=udp
Table=100
Priority=2001
# 其他流量走 101 表(黑洞)
[RoutingPolicyRule]
From=**********/20
To=0.0.0.0/0
Table=101
Priority=2100
EOF

# 获取所有剩余的网卡并添加到 br-lan 网桥中
ip link | grep -Pi '^\d+: (eth|ens|enp|eno)\S*:' | awk -F': ' '{print $2}' | while read -r iface; do
    iface=$(echo $iface | xargs) # 去除空白字符
    # 检查网卡是否为 WAN 接口，若是则跳过
    if [[ "$iface" == "$WAN_IFACE" ]]; then
        echo "跳过网卡: $iface (WAN 接口)"
        continue
    fi
    echo "添加网卡: $iface 到 br-lan 网桥"
    cat <<EOF | tee /etc/systemd/network/10-$iface.network
[Match]
Name=$iface

[Network]
Bridge=br-lan
EOF
done

# 安装 xrouter
mkdir -p /usr/xrouter
# 让用户输入 机器id
read -p "请输入机器id： " MACHINE_CODE
# 让用户输入 机器密钥
read -p "请输入机器密钥： " MACHINE_SECRET
# 让用户选择线上or测试环境
echo "1. 线上环境(https://vnet2.enicfg.com) 2.测试环境(http://*********:8080)"
read -p "请选择环境： " ENV
if [[ "$ENV" == "1" ]]; then
    GATEWAY="https://vnet2.enicfg.com"
    EXPORT="***********:8900"
else
    GATEWAY="http://*********:8080"
    EXPORT="*********:8900"
fi

# 询问是否开启 WireGuard
#read -p "是否开启 WireGuard (yes/no): " enable_wg
#if [[ "$enable_wg" == "yes" || "$enable_wg" == "Yes" ]]; then
#read -p "请输入wg0 Endpoint:" Endpoint0
#read -p "请输入wg0 PrivateKey:" PrivateKey0
#read -p "请输入wg0 PublicKey:" PublicKey0
#read -p "请输入wg0 Address:" Address0
#read -p "请输入wg1 Endpoint:" Endpoint1
#read -p "请输入wg1 PrivateKey:" PrivateKey1
#read -p "请输入wg1 PublicKey:" PublicKey1
#read -p "请输入wg1 Address:" Address1
#cat <<EOF |  tee /etc/systemd/network/40-wg0.netdev
#[NetDev]
#Name = wg0
#Kind = wireguard
#Description = wg dev
#
#[WireGuard]
#PrivateKey=$PrivateKey0
#[WireGuardPeer]
#PublicKey = $PublicKey0
#AllowedIPs = 0.0.0.0/0
#Endpoint = 127.0.0.1:7788
#PersistentKeepalive = 25
#EOF
#cat <<EOF |  tee /etc/systemd/network/40-wg1.netdev
#[NetDev]
#Name = wg1
#Kind = wireguard
#Description = wg dev
#
#[WireGuard]
#PrivateKey=$PrivateKey1
#[WireGuardPeer]
#PublicKey = $PublicKey1
#AllowedIPs = 0.0.0.0/0
#Endpoint = 127.0.0.1:7789
#PersistentKeepalive = 25
#EOF
#cat <<EOF |  tee /etc/systemd/network/40-wg0.network
#[Match]
#Name = wg0
#
#[Network]
#Address = $Address0
#[Link]
#MTUBytes=1280
#[Route]
#Gateway=0.0.0.0
#Table=102
#EOF
#cat <<EOF |  tee /etc/systemd/network/40-wg1.network
#[Match]
#Name = wg1
#
#[Network]
#Address = $Address1
#[Link]
#MTUBytes=1280
#[Route]
#Gateway=0.0.0.0
#Table=103
#EOF
#iptables -A FORWARD -i wg0 -o br-lan -j ACCEPT
#iptables -A FORWARD -i br-lan -o wg0 -j ACCEPT
#iptables -A FORWARD -i wg1 -o br-lan -j ACCEPT
#iptables -A FORWARD -i br-lan -o wg1 -j ACCEPT
#iptables -t nat -I POSTROUTING -o wg+ -j MASQUERADE
#fi

mkdir -p /etc/xrouter
cat <<EOF >/etc/xrouter/config-local.json
{
    "device_code": "$MACHINE_CODE",
    "secret": "$MACHINE_SECRET",
    "domain": "x.net",
    "dsn": "http://<EMAIL>:9988/3",
    "export": "$EXPORT",
    "log_level": "info",
    "clash_log_level": "error",
    "clash_api_address": "127.0.0.1:5123",
    "clash_proxy_port": 5124,
    "pprof": true,
    "cloud_gateway": "$GATEWAY",
    "export_log": "https://vnet3.enicfg.com/loki/api/v1/push",
    "export_log_basic_auth": {
        "user_name": "wuzhen",
        "password": "ROyhNE![1-vS"
    },
    "dhcp": {
        "enable": true,
        "lease": 604800,
        "if_name": "br-lan"
    },
    "flags": [
        "wifi_disable",
        "jump_change_disable",
        "jump3_disable",
        "esim_disable"
    ]
}
EOF
cp $BIN_FILE /usr/xrouter/xrouter
chmod +x /usr/xrouter/xrouter
cat <<EOF | tee /etc/systemd/system/xrouter.service
[Unit]
Description=xrouter
After=network.target

[Service]
ExecStart=/usr/xrouter/start.sh
ExecReload=/usr/xrouter/reload.sh
AmbientCapabilities = CAP_NET_ADMIN
WorkingDirectory=/usr/xrouter
Restart=always
User=root
Environment=PATH=/usr/bin:/usr/local/bin:/usr/sbin:/usr/local/sbin

[Install]
WantedBy=multi-user.target
EOF

cat <<'EOF' >/usr/xrouter/start.sh
#!/bin/bash
if [ -e /usr/xrouter/xrouter_new ]; then
    # 定义文件路径
    FILE_PATH="/usr/xrouter/xrouter_new"
    HASH_FILE_PATH="/usr/xrouter/xrouter_new_sha256"

    # 计算文件的 SHA-256 哈希值
    computed_hash=$(sha256sum "$FILE_PATH" | awk '{ print $1 }')

    # 从哈希文件中读取预期的哈希值
    expected_hash=$(cat "$HASH_FILE_PATH")

    # 比较哈希值
    if [[ "$computed_hash" == "$expected_hash" ]]; then
        mv -f /usr/xrouter/xrouter_new /usr/xrouter/xrouter
    fi
fi
chmod +x /usr/xrouter/xrouter
/usr/xrouter/xrouter
EOF
chmod +x /usr/xrouter/start.sh

cat <<'EOF' >/usr/xrouter/reload.sh
#!/bin/bash

echo "Reloading xrouter..."
pkill -HUP -f /usr/xrouter/xrouter
echo "Reload complete."
EOF
chmod +x /usr/xrouter/reload.sh

# udhcp
cat <<'EOF' >/etc/udhcpc/default.script
#!/bin/sh
# Busybox udhcpc dispatcher script.

log() {
    logger -t "udhcpc[$PPID]" -p daemon.$1 "$interface: $2"
}

case $1 in
    bound|renew)
        # Configure new IP address.
        busybox ifconfig $interface ${mtu:+mtu $mtu} \
            $ip netmask $subnet ${broadcast:+broadcast $broadcast}

        # get current ("old") routes (after setting new IP)
        crouter=$(busybox ip -4 route show dev $interface |
                  busybox awk '$1 == "default" { print $3; }')
        router="${router%% *}" # linux kernel supports only one (default) route
        if [ "$router" != "$crouter" ]; then
            # reset just default routes
            busybox ip -4 route flush exact 0.0.0.0/0 dev $interface
        fi
        if [ -n "$router" ]; then
            # special case for /32 subnets: use onlink keyword
            [ "$subnet" = "***************" ] && onlink=onlink || onlink=
            busybox ip -4 route add default via $router dev $interface $onlink metric 200
        fi
        ;;

    deconfig)
        busybox ip link set $interface up
        busybox ip -4 addr flush dev $interface
        busybox ip -4 route flush dev $interface
        [ -x /sbin/resolvconf ] && resolvconf -d "$interface.udhcpc"
        log notice "deconfigured"
        ;;

    leasefail | nak)
        log err "configuration failed: $1: $message"
        ;;

    *)
        echo "$0: Unknown udhcpc command: $1" >&2
        exit 1
        ;;
esac
EOF
chmod +x /etc/udhcpc/default.script

# 日志留存限制
sed -i "s|^.*\(SIZE\)=.*$|\1=128M|" /etc/default/armbian-ramlog
sed -i "s|^SystemMaxUse=.*|SystemMaxUse=1G|g" /etc/systemd/journald.conf

rm -rf /etc/resolv.conf
# 固定默认dns
cat <<EOF | tee /etc/resolv.conf
nameserver ***************
nameserver *********
EOF

# 设置systemd-networkd-wait-online服务只关注WAN口状态
mkdir -p /etc/systemd/system/systemd-networkd-wait-online.service.d && touch $_/override.conf
cat <<EOF | tee $_
[Service]
ExecStart=
ExecStart=/usr/lib/systemd/systemd-networkd-wait-online -i br-wan:routable --timeout 60
EOF

# Kill-Switch
iptables -F FORWARD
iptables -P FORWARD DROP
iptables -A FORWARD -i tun0 -o br-lan -j ACCEPT
iptables -A FORWARD -i br-lan -o tun0 -j ACCEPT
#iptables -I FORWARD -s **********/20 -p tcp -m connlimit --connlimit-above 100 -j REJECT --reject-with tcp-reset
iptables -I FORWARD -s **********/20 -p tcp -m connlimit --connlimit-above 100 -j NFLOG --nflog-group 100 --nflog-prefix "ConnLimit"
iptables-save >/etc/iptables/rules.v4
cat <<EOF | tee /etc/systemd/system/armbian-restore-iptables.service
[Unit]
Description=Restore IP tables

[Service]
Type=oneshot
ExecStart=/sbin/iptables-restore < /etc/iptables/rules.v4
RemainAfterExit=yes

[Install]
WantedBy=multi-user.target
EOF

# 网络参数修改
sysctl_conf="/etc/sysctl.d/100-custom.conf"
touch $sysctl_conf
cat <<'EOF' >$sysctl_conf
# 启用 IP 转发
net.ipv4.ip_forward = 1
# 设置系统可打开的最大文件数
fs.file-max = 1000000
# 设置路由缓存回收超时时间
net.ipv4.route.gc_timeout = 100
# 设置网络设备接收队列的最大长度
net.core.netdev_max_backlog = 32768
# 设置 TCP 拥塞控制算法为 BBR
net.ipv4.tcp_congestion_control = bbr
# 增加待处理的 SYN 请求数量
net.ipv4.tcp_max_syn_backlog = 4096
# 提高系统的最大监听队列长度
net.core.somaxconn = 2048
# 增加 TIME_WAIT 状态的最大连接数，防止短时间内连接过多导致的资源耗尽
net.ipv4.tcp_max_tw_buckets = 200000
# 减少 TCP 连接的时间等待状态允许重用处于 TIME_WAIT 状态的连接，加快 TCP 连接的建立
net.ipv4.tcp_tw_reuse = 1
# 最大持续时间
net.ipv4.tcp_fin_timeout=30
# 禁止 ICMP 重定向
net.ipv4.conf.all.send_redirects = 0
net.ipv4.conf.default.send_redirects = 0
net.netfilter.nf_conntrack_helper = 1
EOF
sysctl --system
echo "成功设置sysctl"

touch /etc/modules-load.d/ip_nat_pptp.conf
cat <<EOF | tee /etc/modules-load.d/ip_nat_pptp.conf
ip_nat_pptp
EOF
modprobe ip_nat_pptp
echo "成功加载ip_nat_pptp模块"

systemctl daemon-reload
# 关闭 自动更新
# rm /etc/cron.daily/dpkg
echo unattended-upgrades unattended-upgrades/enable_auto_updates boolean false | debconf-set-selections
dpkg-reconfigure -f noninteractive unattended-upgrades
systemctl stop unattended-upgrades
systemctl disable unattended-upgrades

# 关闭 Apache 服务 （如果存在）
systemctl stop apache2.service
systemctl disable apache2.service

# 关闭本地 DNS （如果存在）
systemctl stop systemd-resolved
systemctl disable systemd-resolved

# 关闭 NetworkManager 服务（如果存在）
systemctl stop NetworkManager
systemctl disable NetworkManager

# 关闭 networking 服务（如果存在）
systemctl stop networking
systemctl disable networking

# 启用并启动 xrouter 服务
systemctl enable xrouter.service

# 启用并启动 systemd-networkd 服务
systemctl enable systemd-networkd
systemctl restart systemd-networkd.service

# 启用并启动 systemd-networkd-wait-online 服务
systemctl enable systemd-networkd-wait-online
systemctl restart systemd-networkd-wait-online

#armbian-update
