#!/bin/bash

SERVICE_FILE="/etc/systemd/system/xproxy.service"
# 获取系统架构
ARCH=$(uname -m)
BIN_FILE="xproxy_arm"
# 判断架构类型
if [[ "$ARCH" == "x86_64" ]]; then
    BIN_FILE="xproxy_amd64"
fi
# 检查文件是否存在
if [ -f "$SERVICE_FILE" ]; then
    echo "Detected that the agent has been installed"
    read -p "Do you want to reinstall? (y/n): " choice
    if [ "$choice" != "y" ]; then
        return 0
    fi
fi

read -p "Please enter the proxy name: " name
read -p "Please enter the ip address: " ip
read -p "Please enter the port number (1024-65535): " port
if ! [[ "$port" =~ ^[0-9]+$ ]] || [ "$port" -lt 1024 ] || [ "$port" -gt 65535 ]; then
    echo "Invalid port number. Please make sure to enter a number between 1024 and 65535."
    exit 1
fi
password=$(< /dev/urandom tr -dc 'A-Za-z0-9' | head -c 12; echo)
mkdir /usr/xproxy
mv $BIN_FILE /usr/xproxy/xproxy
chmod +x /usr/xproxy/xproxy
cat <<EOF |  tee /etc/systemd/system/xproxy.service
[Unit]
Description=xproxy
After=network.target

[Service]
ExecStart=/usr/xproxy/xproxy -p $port -k $password
Type=simple
Restart=on-failure

[Install]
WantedBy=multi-user.target
EOF
systemctl daemon-reload
systemctl enable xproxy
systemctl restart xproxy
echo "Xproxy Deployment is complete."
out=$(echo -n "ss://aes-256-gcm:$password@$ip:$port" | base64)
echo "Import via code: $out#$name"
