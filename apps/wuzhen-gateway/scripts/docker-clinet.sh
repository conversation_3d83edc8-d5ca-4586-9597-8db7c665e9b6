#!/bin/bash

# 更新系统和安装必要的软件
apt-get update
apt-get install -y arping wget
# 让用户输入 IP 地址
read -p "请输入要配置的 IP 地址（网段：**********/20）： " IP

# 使用 arping 检查 IP 地址是否被占用
if arping -I "br-lan" -c 1 "$IP" &> /dev/null; then
    echo "IP 地址 $IP 被占用！"
    exit 1
fi
# 安装 Docker
curl -fsSL https://get.docker.com | bash -s docker

# 下载并安装 pipework
wget -O /usr/bin/pipework https://raw.githubusercontent.com/jpetazzo/pipework/master/pipework
chmod +x /usr/bin/pipework

# 运行 Docker 容器
docker run -itd --rm --net=none --name=ubuntu ubuntu

# 配置 Docker 网络
pipework br-lan ubuntu "$IP/20@**********"

# 设置容器的 DNS
docker exec -it ubuntu /bin/bash -c 'echo -e "nameserver **********" | tee /etc/resolv.conf > /dev/null'

# 安装必要的工具
docker exec -it ubuntu /bin/bash -c "apt update && apt install -y iputils-ping curl net-tools iproute2"

# 进入容器的 bash shell
docker exec -it ubuntu /bin/bash
