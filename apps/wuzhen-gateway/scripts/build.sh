#!/bin/bash
#set -eux
# 初始化一个变量来标记是否找到 -arm 参数
ARM=false
LOG=false
# 遍历所有传递的参数
for arg in "$@"; do
    if [[ "$arg" == "-arm" ]]; then
        ARM=true
    fi
    if [[ "$arg" == "-l" ]]; then
        LOG=true
    fi
done
git lfs install
git lfs pull
VERSION=$(git tag --list '*gateway' | sort -V | tail -n 1)
if [ "$VERSION" == "" ]; then
  VERSION="v1.0.0"
fi
TIMESTAMP=$(date +%s)
echo "version: ${VERSION}"
echo "timestamp: ${TIMESTAMP}"
rm -rf build
mkdir -p build
rm -rf  web/content
mkdir -p web/content
cp deploy/deploy.sh build/deploy.sh
cp deploy/deploy-xproxy.sh build/deploy-xproxy.sh
chmod +x build/deploy.sh
chmod +x build/deploy-xproxy.sh
CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -trimpath -ldflags "-extldflags '-static' -s -w" -o build/xproxy_amd64 gateway/cmd/xproxy
CGO_ENABLED=0 GOOS=linux GOARCH=arm64 go build -trimpath -ldflags "-extldflags '-static' -s -w" -o build/xproxy_arm64 gateway/cmd/xproxy
chmod +x build/xproxy_amd64
chmod +x build/xproxy_arm64
cp cmd/xproxy/README.md build/README.md
tar -czvf build/xproxy.tar.gz -C build deploy-xproxy.sh xproxy_amd64 xproxy_arm64 README.md
mv build/xproxy.tar.gz web/res/xproxy.tar.gz
echo "get web"
rm -rf web/content*
#拉取前端更新
cd ../wuzhen-fe || exit
pnpm install
pnpm build
cp -rf web ../wuzhen-gateway/web/content
cd ../wuzhen-gateway || exit
cp -rf web/res web/content/res
CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -trimpath -ldflags "-extldflags '-static' -s -w -X gateway/internal/config.BuildTime=$TIMESTAMP -X gateway/internal/config.Version=$VERSION" -o build/gateway_amd64 gateway/cmd/gateway
CGO_ENABLED=0 GOOS=linux GOARCH=arm64 go build -trimpath -ldflags "-extldflags '-static' -s -w -X gateway/internal/config.BuildTime=$TIMESTAMP -X gateway/internal/config.Version=$VERSION" -o build/gateway_arm64 gateway/cmd/gateway
if $ARM; then
  cp build/gateway_arm64 build/xrouter
else
  cp build/gateway_amd64 build/xrouter
fi
chmod +x build/xrouter
tar -czvf build/deploy.tar.gz -C build deploy.sh gateway_arm64 gateway_amd64
if [ -n "$1" ] && [[ "$1" != "-arm" ]]; then
 ssh root@"$1" 'systemctl stop xrouter.service && rm /usr/xrouter/xrouter'
 scp build/xrouter root@"$1":/usr/xrouter/xrouter
 if $LOG; then
    ssh root@"$1" 'systemctl start xrouter.service && journalctl -u xrouter -f'
 else
    ssh root@"$1" 'systemctl start xrouter.service'
 fi
fi
